/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated by scripts/releases/set-version.js
 */

package com.facebook.react.modules.systeminfo;

import com.facebook.react.common.MapBuilder;

import java.util.Map;

public class ReactNativeVersion {
  public static final Map<String, Object> VERSION = MapBuilder.<String, Object>of(
      "major", 0,
      "minor", 79,
      "patch", 5,
      "prerelease", null);
}
