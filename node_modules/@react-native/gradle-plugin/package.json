{"name": "@react-native/gradle-plugin", "version": "0.79.5", "description": "<PERSON><PERSON><PERSON> Plugin for React Native", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/gradle-plugin"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/gradle-plugin#readme", "keywords": ["gradle", "plugin", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "scripts": {"build": "./gradlew build", "clean": "./gradlew clean", "test": "./gradlew check"}, "files": ["settings.gradle.kts", "build.gradle.kts", "gradle", "gradlew", "gradlew.bat", "README.md", "react-native-gradle-plugin", "settings-plugin", "shared", "shared-testutil"]}