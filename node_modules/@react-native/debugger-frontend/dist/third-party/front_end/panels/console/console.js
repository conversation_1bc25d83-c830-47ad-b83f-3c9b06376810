import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import*as n from"../../core/sdk/sdk.js";import*as o from"../../ui/legacy/legacy.js";import*as i from"../../models/text_utils/text_utils.js";import*as r from"../../core/root/root.js";import*as a from"../../third_party/codemirror.next/codemirror.next.js";import*as l from"../../ui/components/text_editor/text_editor.js";import*as c from"../../ui/legacy/components/object_ui/object_ui.js";import*as h from"../../ui/visual_logging/visual_logging.js";import*as d from"../../ui/components/icon_button/icon_button.js";import*as u from"../../ui/legacy/components/utils/utils.js";import*as p from"../../core/host/host.js";import*as m from"../../models/bindings/bindings.js";import*as g from"../../models/logs/logs.js";import*as f from"../../models/workspace/workspace.js";import*as v from"../../ui/components/code_highlighter/code_highlighter.js";import*as b from"../../ui/components/issue_counter/issue_counter.js";import*as x from"../../ui/components/request_link_icon/request_link_icon.js";import*as C from"../../ui/legacy/components/data_grid/data_grid.js";import*as w from"../../models/formatter/formatter.js";import*as S from"../../models/source_map_scopes/source_map_scopes.js";import*as I from"../../models/issues_manager/issues_manager.js";const y=new CSSStyleSheet;y.replaceSync(":host{padding:2px 1px 2px 2px;white-space:nowrap;display:flex;flex-direction:column;height:36px;justify-content:center;overflow-y:auto}.title{overflow:hidden;text-overflow:ellipsis;flex-grow:0}.badge{pointer-events:none;margin-right:4px;display:inline-block;height:15px}.subtitle{color:var(--sys-color-token-subtle);margin-right:3px;overflow:hidden;text-overflow:ellipsis;flex-grow:0}:host(.highlighted) .subtitle{color:inherit}\n/*# sourceURL=consoleContextSelector.css */\n");const M={javascriptContextNotSelected:"JavaScript context: Not selected",extension:"Extension",javascriptContextS:"JavaScript context: {PH1}"},E=t.i18n.registerUIStrings("panels/console/ConsoleContextSelector.ts",M),T=t.i18n.getLocalizedString.bind(void 0,E);class k{items;dropDown;toolbarItemInternal;constructor(){this.items=new o.ListModel.ListModel,this.dropDown=new o.SoftDropDown.SoftDropDown(this.items,this,"javascript-context"),this.dropDown.setRowHeight(36),this.toolbarItemInternal=new o.Toolbar.ToolbarItem(this.dropDown.element),this.toolbarItemInternal.setEnabled(!1),this.toolbarItemInternal.setTitle(T(M.javascriptContextNotSelected)),this.items.addEventListener("ItemsReplaced",(()=>this.toolbarItemInternal.setEnabled(Boolean(this.items.length)))),this.toolbarItemInternal.element.classList.add("toolbar-has-dropdown"),n.TargetManager.TargetManager.instance().addModelListener(n.RuntimeModel.RuntimeModel,n.RuntimeModel.Events.ExecutionContextCreated,this.onExecutionContextCreated,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.RuntimeModel.RuntimeModel,n.RuntimeModel.Events.ExecutionContextChanged,this.onExecutionContextChanged,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.RuntimeModel.RuntimeModel,n.RuntimeModel.Events.ExecutionContextDestroyed,this.onExecutionContextDestroyed,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.FrameNavigated,this.frameNavigated,this,{scoped:!0}),o.Context.Context.instance().addFlavorChangeListener(n.RuntimeModel.ExecutionContext,this.executionContextChangedExternally,this),o.Context.Context.instance().addFlavorChangeListener(n.DebuggerModel.CallFrame,this.callFrameSelectedInUI,this),n.TargetManager.TargetManager.instance().observeModels(n.RuntimeModel.RuntimeModel,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.DebuggerModel.DebuggerModel,n.DebuggerModel.Events.CallFrameSelected,this.callFrameSelectedInModel,this)}toolbarItem(){return this.toolbarItemInternal}highlightedItemChanged(e,t,s,o){if(n.OverlayModel.OverlayModel.hideDOMNodeHighlight(),t&&t.frameId){const e=n.FrameManager.FrameManager.instance().getFrame(t.frameId);e&&!e.isOutermostFrame()&&e.highlight()}s&&s.classList.remove("highlighted"),o&&o.classList.add("highlighted")}titleFor(e){const t=e.target(),s=e.label();let o=s?t.decorateLabel(s):"";if(e.frameId){const s=t.model(n.ResourceTreeModel.ResourceTreeModel),i=s&&s.frameForId(e.frameId);i&&(o=o||i.displayName())}return o=o||e.origin,o}depthFor(e){let t=e.target(),s=0;if(e.isDefault||s++,e.frameId){let o=n.FrameManager.FrameManager.instance().getFrame(e.frameId);for(;o;)o=o.parentFrame(),o&&(s++,t=o.resourceTreeModel().target())}let o=0,i=t.parentTarget();for(;i&&t.type()!==n.Target.Type.ServiceWorker;)o++,t=i,i=t.parentTarget();return s+=o,s}executionContextCreated(e){this.items.insertWithComparator(e,e.runtimeModel.executionContextComparator()),e===o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext)&&this.dropDown.selectItem(e)}onExecutionContextCreated(e){const t=e.data;this.executionContextCreated(t)}onExecutionContextChanged(e){const t=e.data;-1!==this.items.indexOf(t)&&(this.executionContextDestroyed(t),this.executionContextCreated(t))}executionContextDestroyed(e){const t=this.items.indexOf(e);-1!==t&&this.items.remove(t)}onExecutionContextDestroyed(e){const t=e.data;this.executionContextDestroyed(t)}executionContextChangedExternally({data:e}){e&&!n.TargetManager.TargetManager.instance().isInScope(e.target())||this.dropDown.selectItem(e)}isTopContext(e){if(!e||!e.isDefault)return!1;const t=e.target().model(n.ResourceTreeModel.ResourceTreeModel),s=e.frameId&&t&&t.frameForId(e.frameId);return!!s&&s.isOutermostFrame()}hasTopContext(){return this.items.some((e=>this.isTopContext(e)))}modelAdded(e){e.executionContexts().forEach(this.executionContextCreated,this)}modelRemoved(e){for(let t=this.items.length-1;t>=0;t--)this.items.at(t).runtimeModel===e&&this.executionContextDestroyed(this.items.at(t))}createElementForItem(e){const t=document.createElement("div"),n=o.UIUtils.createShadowRootWithCoreStyles(t,{cssFile:[y],delegatesFocus:void 0}),i=n.createChild("div","title");o.UIUtils.createTextChild(i,s.StringUtilities.trimEndWithMaxLength(this.titleFor(e),100));const r=n.createChild("div","subtitle");return o.UIUtils.createTextChild(r,this.subtitleFor(e)),t.style.paddingLeft=8+15*this.depthFor(e)+"px",t}subtitleFor(t){const s=t.target();let o=null;if(t.frameId){const e=s.model(n.ResourceTreeModel.ResourceTreeModel);o=e&&e.frameForId(t.frameId)}if(e.ParsedURL.schemeIs(t.origin,"chrome-extension:"))return T(M.extension);const i=o&&o.sameTargetParentFrame();if(!o||!i||i.securityOrigin!==t.origin){const s=e.ParsedURL.ParsedURL.fromString(t.origin);if(s)return s.domain()}if(o&&o.securityOrigin){const t=new e.ParsedURL.ParsedURL(o.securityOrigin).domain();if(t)return t}return"IFrame"}isItemSelectable(e){const t=e.debuggerModel.selectedCallFrame(),s=t&&t.script.executionContext();return!s||e===s}itemSelected(e){this.toolbarItemInternal.element.classList.toggle("highlight",!this.isTopContext(e)&&this.hasTopContext());const t=e?T(M.javascriptContextS,{PH1:this.titleFor(e)}):T(M.javascriptContextNotSelected);this.toolbarItemInternal.setTitle(t),o.Context.Context.instance().setFlavor(n.RuntimeModel.ExecutionContext,e)}callFrameSelectedInUI(){const e=o.Context.Context.instance().flavor(n.DebuggerModel.CallFrame),t=e&&e.script.executionContext();t&&o.Context.Context.instance().setFlavor(n.RuntimeModel.ExecutionContext,t)}callFrameSelectedInModel(e){const t=e.data;for(const e of this.items)e.debuggerModel===t&&this.dropDown.refreshItem(e)}frameNavigated(e){const t=e.data,s=t.resourceTreeModel().target().model(n.RuntimeModel.RuntimeModel);if(s)for(const e of s.executionContexts())t.id===e.frameId&&this.dropDown.refreshItem(e)}}var F,L=Object.freeze({__proto__:null,ConsoleContextSelector:k});class R{name;parsedFilters;executionContext;levelsMask;constructor(e,t,s,n){this.name=e,this.parsedFilters=t,this.executionContext=s,this.levelsMask=n||R.defaultLevelsFilterValue()}static allLevelsFilterValue(){const e={},t={Verbose:"verbose",Info:"info",Warning:"warning",Error:"error"};for(const s of Object.values(t))e[s]=!0;return e}static defaultLevelsFilterValue(){const e=R.allLevelsFilterValue();return e.verbose=!1,e}static singleLevelMask(e){const t={};return t[e]=!0,t}clone(){const e=this.parsedFilters.map(i.TextUtils.FilterParser.cloneFilter),t=Object.assign({},this.levelsMask);return new R(this.name,e,this.executionContext,t)}shouldBeVisible(e){const t=e.consoleMessage();return(!this.executionContext||this.executionContext.runtimeModel===t.runtimeModel()&&this.executionContext.id===t.getExecutionContextId())&&(t.type===n.ConsoleModel.FrontendMessageType.Command||t.type===n.ConsoleModel.FrontendMessageType.Result||"endGroup"===t.type||!(t.level&&!this.levelsMask[t.level])&&(this.applyFilter(e)||this.parentGroupHasMatch(e.consoleGroup())))}parentGroupHasMatch(e){return null!==e&&(this.applyFilter(e)||this.parentGroupHasMatch(e.consoleGroup()))}applyFilter(e){const t=e.consoleMessage();for(const o of this.parsedFilters)if(o.key)switch(o.key){case F.Context:if(!s(o,t.context,!1))return!1;break;case F.Source:if(!s(o,t.source?n.ConsoleModel.MessageSourceDisplayName.get(t.source):t.source,!0))return!1;break;case F.Url:if(!s(o,t.url,!1))return!1}else{if(o.regex&&e.matchesFilterRegex(o.regex)===o.negative)return!1;if(o.text&&e.matchesFilterText(o.text)===o.negative)return!1}return!0;function s(e,t,s){if(!e.text)return Boolean(t)===e.negative;if(!t)return!e.text==!e.negative;const n=e.text.toLowerCase(),o=t.toLowerCase();return(!s||o===n!==e.negative)&&!(!s&&o.includes(n)===e.negative)}}}!function(e){e.Context="context",e.Source="source",e.Url="url"}(F||(F={}));var A=Object.freeze({__proto__:null,ConsoleFilter:R,get FilterType(){return F}});const P=["black","red","green","yellow","blue","magenta","cyan","gray"],U=["darkgray","lightred","lightgreen","lightyellow","lightblue","lightmagenta","lightcyan","white"],B=(e,t)=>{const s=[],n=new Map;function o(e){const t=n.get("text-decoration")??"";t.includes(e)||n.set("text-decoration",`${t} ${e}`)}function i(e){const t=n.get("text-decoration")?.replace(` ${e}`,"");t?n.set("text-decoration",t):n.delete("text-decoration")}function r(e){e&&(s.length&&"string"===s[s.length-1].type?s[s.length-1].value+=e:s.push({type:"string",value:e}))}let a=0;const l=/%([%_Oocsdfi])|\x1B\[([\d;]*)m/;for(let c=l.exec(e);null!==c;c=l.exec(e)){let l;r(c.input.substring(0,c.index));const h=c[1];switch(h){case"%":r("%"),l="";break;case"s":if(a<t.length){const{description:e}=t[a++];l=e??""}break;case"c":if(a<t.length){const e="style",n=t[a++].description??"";s.push({type:e,value:n}),l=""}break;case"o":case"O":if(a<t.length){const e="O"===h?"generic":"optimal",n=t[a++];s.push({type:e,value:n}),l=""}break;case"_":a<t.length&&(a++,l="");break;case"d":case"f":case"i":if(a<t.length){const{value:e}=t[a++];l="number"!=typeof e?NaN:e,"f"!==h&&(l=Math.floor(l))}break;case void 0:{const e=(c[2]||"0").split(";").map((e=>e?parseInt(e,10):0));for(;e.length;){const t=e.shift();switch(t){case 0:n.clear();break;case 1:n.set("font-weight","bold");break;case 2:n.set("font-weight","lighter");break;case 3:n.set("font-style","italic");break;case 4:o("underline");break;case 9:o("line-through");break;case 22:n.delete("font-weight");break;case 23:n.delete("font-style");break;case 24:i("underline");break;case 29:i("line-through");break;case 38:case 48:if(2===e.shift()){const s=e.shift()??0,o=e.shift()??0,i=e.shift()??0;n.set(38===t?"color":"background-color",`rgb(${s},${o},${i})`)}break;case 39:case 49:n.delete(39===t?"color":"background-color");break;case 53:o("overline");break;case 55:i("overline");break;default:{const e=P[t-30]??U[t-90];if(void 0!==e)n.set("color",`var(--console-color-${e})`);else{const e=P[t-40]??U[t-100];void 0!==e&&n.set("background-color",`var(--console-color-${e})`)}break}}}const t=[...n.entries()].map((([e,t])=>`${e}:${t.trimStart()}`)).join(";"),r="style";s.push({type:r,value:t}),l="";break}}void 0===l&&(r(c[0]),l=""),e=l+c.input.substring(c.index+c[0].length)}return r(e),{tokens:s,args:t.slice(a)}},H=(t,s)=>{const n=["background","border","color","font","line","margin","padding","text"],o=/url\([\'\"]?([^\)]*)/g;t.clear();const i=document.createElement("span");i.setAttribute("style",s);for(const s of i.style){if(!n.some((e=>s.startsWith(e)||s.startsWith(`-webkit-${e}`))))continue;const r=i.style.getPropertyValue(s);[...r.matchAll(o)].map((e=>e[1])).some((t=>!e.ParsedURL.schemeIs(t,"data:")))||t.set(s,{value:r,priority:i.style.getPropertyPriority(s)})}};var j=Object.freeze({__proto__:null,format:B,updateStyle:H});const O=new CSSStyleSheet;O.replaceSync(".value.object-value-node:hover{background-color:var(--sys-color-state-hover-on-subtle)}.object-value-function-prefix,\n.object-value-boolean{color:var(--sys-color-token-attribute-value)}.object-value-function{font-style:italic}.object-value-function.linkified:hover{--override-linkified-hover-background:rgb(0 0 0/10%);background-color:var(--override-linkified-hover-background);cursor:pointer}.theme-with-dark-background .object-value-function.linkified:hover,\n:host-context(.theme-with-dark-background) .object-value-function.linkified:hover{--override-linkified-hover-background:rgb(230 230 230/10%)}.object-value-number{color:var(--sys-color-token-attribute-value)}.object-value-bigint{color:var(--sys-color-token-comment)}.object-value-string,\n.object-value-regexp,\n.object-value-symbol{white-space:pre;unicode-bidi:-webkit-isolate;color:var(--sys-color-token-property-special)}.object-value-node{position:relative;vertical-align:baseline;color:var(--sys-color-token-variable);white-space:nowrap}.object-value-null,\n.object-value-undefined{color:var(--sys-color-state-disabled)}.object-value-unavailable{color:var(--sys-color-token-tag)}.object-value-calculate-value-button:hover{text-decoration:underline}.object-properties-section-custom-section{display:inline-flex;flex-direction:column}.theme-with-dark-background .object-value-number,\n:host-context(.theme-with-dark-background) .object-value-number,\n.theme-with-dark-background .object-value-boolean,\n:host-context(.theme-with-dark-background) .object-value-boolean{--override-primitive-dark-mode-color:hsl(252deg 100% 75%);color:var(--override-primitive-dark-mode-color)}.object-properties-section .object-description{color:var(--sys-color-token-subtle)}.value .object-properties-preview{white-space:nowrap}.name{color:var(--sys-color-token-tag);flex-shrink:0}.object-properties-preview .name{color:var(--sys-color-token-subtle)}@media (forced-colors: active){.object-value-calculate-value-button:hover{forced-color-adjust:none;color:Highlight}}\n/*# sourceURL=objectValue.css */\n");const V=new CSSStyleSheet;V.replaceSync(".close-button{position:absolute;top:1px;left:3px}.console-pins{max-height:200px;overflow-y:auto;background:var(--sys-color-cdt-base-container);--override-error-text-color:var(--sys-color-on-error-container)}.console-pins:not(:empty){border-bottom:1px solid var(--sys-color-divider)}.console-pin{position:relative;user-select:text;flex:none;padding:2px 0 6px 24px}.console-pin:not(:last-child){border-bottom:1px solid var(--sys-color-divider)}.console-pin.error-level:not(:focus-within){background-color:var(--sys-color-surface-error);color:var(--override-error-text-color)}.console-pin:not(:last-child).error-level:not(:focus-within){border-top:1px solid var(--sys-color-error-outline);border-bottom:1px solid var(--sys-color-error-outline);margin-top:-1px}.console-pin-name{margin-left:8px;margin-bottom:1px;height:auto}.console-pin-name,\n.console-pin-preview{width:100%;text-overflow:ellipsis;white-space:nowrap;min-height:13px}.console-pin-preview{overflow:hidden}.console-pin-name:focus-within{background:var(--sys-color-cdt-base-container);border-radius:4px;border:1px solid var(--sys-color-state-focus-ring)}.console-pin:focus-within .console-pin-preview,\n.console-pin-name:not(:focus-within):not(:hover){opacity:60%}\n/*# sourceURL=consolePinPane.css */\n");const N={removeExpression:"Remove expression",removeAllExpressions:"Remove all expressions",removeExpressionS:"Remove expression: {PH1}",removeBlankExpression:"Remove blank expression",liveExpressionEditor:"Live expression editor",expression:"Expression",evaluateAllowingSideEffects:"Evaluate, allowing side effects",notAvailable:"not available"},G=t.i18n.registerUIStrings("panels/console/ConsolePinPane.ts",N),D=t.i18n.getLocalizedString.bind(void 0,G),W=new WeakMap;class _ extends o.ThrottledWidget.ThrottledWidget{liveExpressionButton;focusOut;pins;pinsSetting;constructor(t,s){super(!0,250),this.liveExpressionButton=t,this.focusOut=s,this.contentElement.classList.add("console-pins","monospace"),this.contentElement.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),this.contentElement.setAttribute("jslog",`${h.pane("console-pins")}`),this.pins=new Set,this.pinsSetting=e.Settings.Settings.instance().createLocalSetting("console-pins",[]);for(const e of this.pinsSetting.get())this.addPin(e)}wasShown(){super.wasShown(),this.registerCSSFiles([V,O])}willHide(){for(const e of this.pins)e.setHovered(!1)}savePins(){const e=Array.from(this.pins).map((e=>e.expression()));this.pinsSetting.set(e)}contextMenuEventFired(e){const t=new o.ContextMenu.ContextMenu(e),s=o.UIUtils.deepElementFromEvent(e);if(s){const e=s.enclosingNodeOrSelfWithClass("console-pin");if(e){const s=W.get(e);s&&(t.editSection().appendItem(D(N.removeExpression),this.removePin.bind(this,s),{jslogContext:"remove-expression"}),s.appendToContextMenu(t))}}t.editSection().appendItem(D(N.removeAllExpressions),this.removeAllPins.bind(this),{jslogContext:"remove-all-expressions"}),t.show()}removeAllPins(){for(const e of this.pins)this.removePin(e)}removePin(e){e.element().remove();const t=this.focusedPinAfterDeletion(e);this.pins.delete(e),this.savePins(),t?t.focus():this.liveExpressionButton.focus()}addPin(e,t){const s=new z(e,this,this.focusOut);this.contentElement.appendChild(s.element()),this.pins.add(s),this.savePins(),t&&s.focus(),this.update()}focusedPinAfterDeletion(e){const t=Array.from(this.pins);for(let s=0;s<t.length;s++)if(t[s]===e)return 1===t.length?null:s===t.length-1?t[s-1]:t[s+1];return null}async doUpdate(){if(!this.pins.size||!this.isShowing())return;this.isShowing()&&this.update();const e=Array.from(this.pins,(e=>e.updatePreview()));await Promise.all(e),this.updatedForTest()}updatedForTest(){}}class z{pinPane;focusOut;pinElement;pinPreview;lastResult;lastExecutionContext;editor;committedExpression;hovered;lastNode;deletePinIcon;constructor(t,s,n){this.pinPane=s,this.focusOut=n,this.deletePinIcon=document.createElement("div",{is:"dt-close-button"}),this.deletePinIcon.classList.add("close-button"),this.deletePinIcon.setTabbable(!0),t.length?this.deletePinIcon.setAccessibleName(D(N.removeExpressionS,{PH1:t})):this.deletePinIcon.setAccessibleName(D(N.removeBlankExpression)),self.onInvokeElement(this.deletePinIcon,(e=>{s.removePin(this),e.consume(!0)}));const i=o.Fragment.Fragment.build`
  <div class='console-pin'>
  ${this.deletePinIcon}
  <div class='console-pin-name' $='name' jslog="${h.textField().track({change:!0})}"></div>
  <div class='console-pin-preview' $='preview'></div>
  </div>`;this.pinElement=i.element(),this.pinPreview=i.$("preview");const r=i.$("name");o.Tooltip.Tooltip.install(r,t),W.set(this.pinElement,this),this.lastResult=null,this.lastExecutionContext=null,this.committedExpression=t,this.hovered=!1,this.lastNode=null,this.editor=this.createEditor(t,r),this.pinPreview.addEventListener("mouseenter",this.setHovered.bind(this,!0),!1),this.pinPreview.addEventListener("mouseleave",this.setHovered.bind(this,!1),!1),this.pinPreview.addEventListener("click",(t=>{this.lastNode&&(e.Revealer.reveal(this.lastNode),t.consume())}),!1),r.addEventListener("keydown",(e=>{"Escape"===e.key&&e.consume()}))}createEditor(e,t){const s=[a.EditorView.contentAttributes.of({"aria-label":D(N.liveExpressionEditor)}),a.EditorView.lineWrapping,a.javascript.javascriptLanguage,l.Config.showCompletionHint,a.placeholder(D(N.expression)),a.keymap.of([{key:"Escape",run:e=>(e.dispatch({changes:{from:0,to:e.state.doc.length,insert:this.committedExpression}}),this.focusOut(),!0)},{key:"Enter",run:()=>(this.focusOut(),!0)},{key:"Mod-Enter",run:()=>(this.focusOut(),!0)},{key:"Tab",run:e=>null===a.completionStatus&&(e.dispatch({changes:{from:0,to:e.state.doc.length,insert:this.committedExpression}}),this.focusOut(),!0)}]),a.EditorView.domEventHandlers({blur:(e,t)=>this.onBlur(t)}),l.Config.baseConfiguration(e),l.Config.closeBrackets.instance(),l.Config.autocompletion.instance()];"true"!==r.Runtime.Runtime.queryParam("noJavaScriptCompletion")&&s.push(l.JavaScript.completion());const n=new l.TextEditor.TextEditor(a.EditorState.create({doc:e,extensions:s}));return t.appendChild(n),n}onBlur(e){const t=e.state.doc.toString(),s=t.trim();this.committedExpression=s,this.pinPane.savePins(),this.committedExpression.length?this.deletePinIcon.setAccessibleName(D(N.removeExpressionS,{PH1:this.committedExpression})):this.deletePinIcon.setAccessibleName(D(N.removeBlankExpression)),e.dispatch({selection:{anchor:s.length},changes:s!==t?{from:0,to:t.length,insert:s}:void 0})}setHovered(e){this.hovered!==e&&(this.hovered=e,!e&&this.lastNode&&n.OverlayModel.OverlayModel.hideDOMNodeHighlight())}expression(){return this.committedExpression}element(){return this.pinElement}async focus(){const e=this.editor;e.editor.focus(),e.dispatch({selection:{anchor:e.state.doc.length}})}appendToContextMenu(e){this.lastResult&&!("error"in this.lastResult)&&this.lastResult.object&&(e.appendApplicableItems(this.lastResult.object),this.lastResult=null)}async updatePreview(){if(!this.editor)return;const e=l.Config.contentIncludingHint(this.editor.editor),t=this.pinElement.hasFocus(),s=t&&e!==this.committedExpression,i=s?250:void 0,r=o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext),{preview:a,result:h}=await c.JavaScriptREPL.JavaScriptREPL.evaluateAndBuildPreview(e,s,!0,i,!t,"console",!0,!0);this.lastResult&&this.lastExecutionContext&&this.lastExecutionContext.runtimeModel.releaseEvaluationResult(this.lastResult),this.lastResult=h||null,this.lastExecutionContext=r||null;const d=a.deepTextContent();if(!d||d!==this.pinPreview.deepTextContent()){if(this.pinPreview.removeChildren(),h&&n.RuntimeModel.RuntimeModel.isSideEffectFailure(h)){const e=this.pinPreview.createChild("span","object-value-calculate-value-button");e.textContent="(…)",o.Tooltip.Tooltip.install(e,D(N.evaluateAllowingSideEffects))}else d?this.pinPreview.appendChild(a):t||o.UIUtils.createTextChild(this.pinPreview,D(N.notAvailable));o.Tooltip.Tooltip.install(this.pinPreview,d)}let u=null;h&&!("error"in h)&&"object"===h.object.type&&"node"===h.object.subtype&&(u=h.object),this.hovered&&(u?n.OverlayModel.OverlayModel.highlightObjectAsDOMNode(u):this.lastNode&&n.OverlayModel.OverlayModel.hideDOMNodeHighlight()),this.lastNode=u||null;const p=h&&!("error"in h)&&h.exceptionDetails&&!n.RuntimeModel.RuntimeModel.isSideEffectFailure(h);this.pinElement.classList.toggle("error-level",Boolean(p))}}var q=Object.freeze({__proto__:null,ConsolePinPane:_,ConsolePin:z});const $=new CSSStyleSheet;$.replaceSync(':host{overflow:auto;background-color:var(--sys-color-cdt-base-container)}.tree-outline-disclosure{max-width:100%;padding-left:6px}.count{flex:none;margin:0 8px}devtools-icon{margin:0 5px;&[name="cross-circle"]{color:var(--sys-color-error-bright)}&[name="warning"]{color:var(--icon-warning)}&[name="info"]{color:var(--icon-info)}}li{height:24px}.tree-element-title{flex-shrink:100;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.tree-outline li:hover:not(.selected) .selection{display:block;background-color:var(--sys-color-state-hover-on-subtle)}@media (forced-colors: active){devtools-icon{color:ButtonText}.tree-outline li:hover:not(.selected) .selection{forced-color-adjust:none;background-color:Highlight}.tree-outline li:hover .tree-element-title,\n  .tree-outline li.selected .tree-element-title,\n  .tree-outline li:hover .count,\n  .tree-outline li.selected .count{forced-color-adjust:none;color:HighlightText}.tree-outline li:hover devtools-icon,\n  .tree-outline li.selected devtools-icon{color:HighlightText!important}}\n/*# sourceURL=consoleSidebar.css */\n');const K={other:"<other>",dUserMessages:"{n, plural, =0 {No user messages} =1 {# user message} other {# user messages}}",dMessages:"{n, plural, =0 {No messages} =1 {# message} other {# messages}}",dErrors:"{n, plural, =0 {No errors} =1 {# error} other {# errors}}",dWarnings:"{n, plural, =0 {No warnings} =1 {# warning} other {# warnings}}",dInfo:"{n, plural, =0 {No info} =1 {# info} other {# info}}",dVerbose:"{n, plural, =0 {No verbose} =1 {# verbose} other {# verbose}}"},J=t.i18n.registerUIStrings("panels/console/ConsoleSidebar.ts",K),X=t.i18n.getLocalizedString.bind(void 0,J);class Z extends(e.ObjectWrapper.eventMixin(o.Widget.VBox)){tree;selectedTreeElement;treeElements;constructor(){super(!0),this.setMinimumSize(125,0),this.tree=new o.TreeOutline.TreeOutlineInShadow,this.tree.addEventListener(o.TreeOutline.Events.ElementSelected,this.selectionChanged.bind(this)),this.contentElement.setAttribute("jslog",`${h.pane("sidebar").track({resize:!0})}`),this.contentElement.appendChild(this.tree.element),this.selectedTreeElement=null,this.treeElements=[];const t=e.Settings.Settings.instance().createSetting("console.sidebar-selected-filter",null),s=[{key:F.Source,text:e.Console.FrontendMessageSource.ConsoleAPI,negative:!1,regex:void 0}];this.appendGroup("message",[],R.allLevelsFilterValue(),d.Icon.create("list"),t),this.appendGroup("user message",s,R.allLevelsFilterValue(),d.Icon.create("profile"),t),this.appendGroup("error",[],R.singleLevelMask("error"),d.Icon.create("cross-circle"),t),this.appendGroup("warning",[],R.singleLevelMask("warning"),d.Icon.create("warning"),t),this.appendGroup("info",[],R.singleLevelMask("info"),d.Icon.create("info"),t),this.appendGroup("verbose",[],R.singleLevelMask("verbose"),d.Icon.create("bug"),t);const n=t.get();(this.treeElements.find((e=>e.name()===n))||this.treeElements[0]).select()}appendGroup(e,t,s,n,o){const i=new R(e,t,null,s),r=new te(i,n,o);this.tree.appendChild(r),this.treeElements.push(r)}clear(){for(const e of this.treeElements)e.clear()}onMessageAdded(e){for(const t of this.treeElements)t.onMessageAdded(e)}shouldBeVisible(e){return!(this.selectedTreeElement instanceof Q)||this.selectedTreeElement.filter().shouldBeVisible(e)}selectionChanged(e){this.selectedTreeElement=e.data,this.dispatchEventToListeners("FilterSelected")}wasShown(){super.wasShown(),this.tree.registerCSSFiles([$])}}class Q extends o.TreeOutline.TreeElement{filterInternal;constructor(e,t){super(e),this.filterInternal=t}filter(){return this.filterInternal}}class Y extends Q{countElement;messageCount;constructor(e){super(e.name,e),this.countElement=this.listItemElement.createChild("span","count");const t=d.Icon.create("document");this.setLeadingIcons([t]),this.messageCount=0}incrementAndUpdateCounter(){this.messageCount++,this.countElement.textContent=`${this.messageCount}`}}const ee=new Map([["user message",K.dUserMessages],["message",K.dMessages],["error",K.dErrors],["warning",K.dWarnings],["info",K.dInfo],["verbose",K.dVerbose]]);class te extends Q{selectedFilterSetting;urlTreeElements;messageCount;uiStringForFilterCount;constructor(e,t,s){super(e.name,e),this.uiStringForFilterCount=ee.get(e.name)||"",this.selectedFilterSetting=s,this.urlTreeElements=new Map,this.setLeadingIcons([t]),this.messageCount=0,this.updateCounter()}clear(){this.urlTreeElements.clear(),this.removeChildren(),this.messageCount=0,this.updateCounter()}name(){return this.filterInternal.name}onselect(e){return this.selectedFilterSetting.set(this.filterInternal.name),super.onselect(e)}updateCounter(){this.title=this.updateGroupTitle(this.messageCount),this.setExpandable(Boolean(this.childCount()))}updateGroupTitle(e){return this.uiStringForFilterCount?X(this.uiStringForFilterCount,{n:e}):""}onMessageAdded(e){const t=e.consoleMessage(),s=t.type!==n.ConsoleModel.FrontendMessageType.Command&&t.type!==n.ConsoleModel.FrontendMessageType.Result&&!t.isGroupMessage();if(!this.filterInternal.shouldBeVisible(e)||!s)return;this.childElement(t.url).incrementAndUpdateCounter(),this.messageCount++,this.updateCounter()}childElement(t){const s=t||null;let n=this.urlTreeElements.get(s);if(n)return n;const o=this.filterInternal.clone(),i=s?e.ParsedURL.ParsedURL.fromString(s):null;return o.name=s?i?i.displayName:s:X(K.other),o.parsedFilters.push({key:F.Url,text:s,negative:!1,regex:void 0}),n=new Y(o),s&&(n.tooltip=s),this.urlTreeElements.set(s,n),this.appendChild(n),n}}var se=Object.freeze({__proto__:null,ConsoleSidebar:Z,URLGroupTreeElement:Y,FilterTreeElement:te});const ne=new CSSStyleSheet;function oe(t,s){if(!/^[\w.]*Error\b/.test(s))return null;const n=t.debuggerModel(),o=t.target().inspectedURL(),i=s.split("\n"),r=[];for(const t of i){const s=/^\s*at\s(async\s)?/.exec(t);if(!s){if(r.length&&r[r.length-1].link)return null;r.push({line:t});continue}let i=s[0].length,a=t.length,l=!1;for(;")"===t[a-1];)for(a--,l=!0;;){if(i=t.indexOf(" (",i),i<0)return null;if(i+=2,!t.substring(i).startsWith("eval at "))break;if(i+=8,a=t.lastIndexOf(", ",a)-1,a<0)return null}const c=t.substring(i,a),h=e.ParsedURL.ParsedURL.splitLineAndColumn(c);if("<anonymous>"===h.url){r.push({line:t});continue}let d=ie(n,h.url);if(!d&&e.ParsedURL.ParsedURL.isRelativeURL(h.url)&&(d=ie(n,e.ParsedURL.ParsedURL.completeURL(o,h.url))),!d)return null;r.push({line:t,link:{url:d,prefix:t.substring(0,i),suffix:t.substring(a),enclosedInBraces:l,lineNumber:h.lineNumber,columnNumber:h.columnNumber}})}return r}function ie(t,s){if(!s)return null;if(e.ParsedURL.ParsedURL.isValidUrlString(s))return s;if(t.scriptsForSourceURL(s).length)return s;const n=new URL(s,"file://");return t.scriptsForSourceURL(n.href).length?n.href:null}function re(e,t){for(const s of e){const e=t.callFrames.find((e=>ae(s,e)));e&&s.link&&(s.link.scriptId=e.scriptId)}}function ae(e,t){if(!e.link)return!1;const{url:s,lineNumber:n,columnNumber:o}=e.link;return s===t.url&&n===t.lineNumber&&o===t.columnNumber}ne.replaceSync('.console-view{background-color:var(--sys-color-cdt-base-container);overflow:hidden;--override-error-text-color:var(--sys-color-on-error-container);--message-corner-rounder-background:var(--sys-color-cdt-base-container)}.console-toolbar-container{display:flex;flex:none}.console-main-toolbar{flex:1 1 auto}.console-toolbar-container > .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.console-view-fix-select-all{height:0;overflow:hidden}.console-settings-pane{flex:none;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.console-settings-pane .toolbar{flex:1 1}#console-messages{flex:1 1;overflow-y:auto;word-wrap:break-word;user-select:text;transform:translateZ(0);overflow-anchor:none;background-color:var(--sys-color-cdt-base-container)}#console-prompt{clear:right;position:relative;margin:0 22px 0 20px}.console-prompt-editor-container{min-height:21px}.console-message,\n.console-user-command{clear:right;position:relative;padding:3px 22px 1px 0;margin-left:24px;min-height:17px;flex:auto;display:flex}.console-message > *{flex:auto}.console-timestamp{color:var(--sys-color-token-subtle);user-select:none;flex:none;margin-right:5px}.message-level-icon,\n.command-result-icon{position:absolute;left:-17px;top:2px;user-select:none}.console-message-repeat-count{margin:1.4px 0 0 10px;flex:none}.repeated-message{margin-left:4px}.repeated-message .message-level-icon{display:none}.console-message-stack-trace-toggle{display:flex;flex-direction:row;align-items:flex-start;margin-top:-1px}.repeated-message .console-message-stack-trace-toggle,\n.repeated-message > .console-message-text{flex:1}.console-error-level .repeated-message,\n.console-warning-level .repeated-message,\n.console-verbose-level .repeated-message,\n.console-info-level .repeated-message{display:flex}.console-info{color:var(--sys-color-token-subtle);font-style:italic;padding-bottom:2px}.console-group .console-group > .console-group-messages{margin-left:16px}.console-group-title.console-from-api{font-weight:bold}.console-group-title .console-message{margin-left:12px}.expand-group-icon{user-select:none;flex:none;position:relative;left:8px;top:3px;margin-right:2px}.console-group-title .message-level-icon{display:none}.console-message-repeat-count .expand-group-icon{position:static;color:var(--sys-color-cdt-base-container);margin-left:-1px}.console-group{position:relative}.console-message-wrapper{display:flex;flex-direction:column;margin:4px;border-radius:5px;--console-color-black:#000;--console-color-red:#a00;--console-color-green:#0a0;--console-color-yellow:#a50;--console-color-blue:#00a;--console-color-magenta:#a0a;--console-color-cyan:#0aa;--console-color-gray:#aaa;--console-color-darkgray:#555;--console-color-lightred:#f55;--console-color-lightgreen:#5f5;--console-color-lightyellow:#ff5;--console-color-lightblue:#55f;--console-color-ightmagenta:#f5f;--console-color-lightcyan:#5ff;--console-color-white:#fff;&:focus{background-color:var(--sys-color-tonal-container);& ::selection{background-color:var(--sys-color-state-focus-select)}}}.console-row-wrapper{display:flex;flex-direction:row}.theme-with-dark-background .console-message-wrapper{--console-color-red:rgb(237 78 76);--console-color-green:rgb(1 200 1);--console-color-yellow:rgb(210 192 87);--console-color-blue:rgb(39 116 240);--console-color-magenta:rgb(161 66 244);--console-color-cyan:rgb(18 181 203);--console-color-gray:rgb(207 208 208);--console-color-darkgray:rgb(137 137 137);--console-color-lightred:rgb(242 139 130);--console-color-lightgreen:rgb(161 247 181);--console-color-lightyellow:rgb(221 251 85);--console-color-lightblue:rgb(102 157 246);--console-color-lightmagenta:rgb(214 112 214);--console-color-lightcyan:rgb(132 240 255)}.console-message-wrapper.console-warning-level + .console-message-wrapper,\n.console-message-wrapper.console-error-level + .console-message-wrapper{& .console-message::before,\n  & .console-user-command::before{display:none!important}}.console-message-wrapper:not(.console-error-level):not(.console-warning-level){& .console-message::before,\n  & .console-user-command::before{width:calc(100% - 25px);content:"";display:block;position:absolute;top:-2px;border-top:1px solid var(--sys-color-divider)}&:first-of-type .console-message::before,\n  &:first-of-type .console-user-command::before{display:none}}.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level){border-top-width:0}.console-message-wrapper:focus + .console-message-wrapper{border-top-color:transparent}.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus{border-top-width:1px}.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .console-message{padding-top:2px;min-height:16px}.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .command-result-icon{top:3px}.console-message-wrapper .nesting-level-marker{width:14px;flex:0 0 auto;position:relative;margin-bottom:-1px;margin-top:-1px;background-color:var(--sys-color-cdt-base-container)}.console-message-wrapper .nesting-level-marker + .console-message::after{position:absolute;left:-30px;top:0;width:6px;height:100%;box-sizing:border-box;background-color:var(--sys-color-surface-yellow);border-top-left-radius:5px;border-bottom-left-radius:5px;content:""}.console-error-level{background-color:var(--sys-color-surface-error);--message-corner-rounder-background:var(--sys-color-surface-error)}.console-warning-level{background-color:var(--sys-color-surface-yellow);--message-corner-rounder-background:var(--sys-color-surface-yellow)}.console-warning-level .console-message-text{color:var(--sys-color-on-surface-yellow)}.console-view-object-properties-section{padding:0;position:relative;vertical-align:baseline;color:inherit;display:inline-block;overflow-wrap:break-word;max-width:100%}.info-note{background-color:var(--sys-color-tonal-container)}.info-note::before{content:"i"}.console-view-object-properties-section:not(.expanded) .info-note{display:none}.console-error-level .console-message-text,\n.console-error-level .console-view-object-properties-section{color:var(--override-error-text-color)!important}.console-system-type.console-info-level{color:var(--sys-color-primary)}#console-messages .link{cursor:pointer;text-decoration:underline}#console-messages .link,\n#console-messages .devtools-link:not(.invalid-link){color:var(--sys-color-primary);word-break:break-all}#console-messages .devtools-link.ignore-list-link{opacity:60%}#console-messages .resource-links{margin-top:-1px;margin-bottom:-2px}.console-object-preview{white-space:normal;word-wrap:break-word;font-style:italic}.console-object-preview .name{flex-shrink:0}.console-message-text .object-value-node{display:inline-block}.console-message-text .object-value-string,\n.console-message-text .object-value-regexp,\n.console-message-text .object-value-symbol{white-space:pre-wrap;word-break:break-all}.console-message-formatted-table{clear:both}.console-message .source-code{line-height:1.2}.console-message-anchor{float:right;text-align:right;max-width:100%;margin-left:4px}.console-message-badge{float:right;margin-left:4px}.console-message-nowrap-below,\n.console-message-nowrap-below div,\n.console-message-nowrap-below span{white-space:nowrap!important}.object-state-note{display:inline-block;width:11px;height:11px;color:var(--sys-color-on-tonal-container);text-align:center;border-radius:3px;line-height:13px;margin:0 6px;font-size:9px}.console-object{white-space:pre-wrap;word-break:break-all}.console-message-stack-trace-wrapper{flex:1 1 auto;display:flex;flex-direction:column;align-items:stretch}.console-message-stack-trace-wrapper > *{flex:none}.console-message-expand-icon{margin-bottom:-4px}.console-searchable-view{max-height:100%}.console-view-pinpane{flex:none;max-height:50%}.message-count{width:0;height:0}devtools-console-insight{margin:9px 22px 11px 24px}.hover-button{--width:24px;align-items:center;background-color:var(--sys-color-cdt-base-container);border-radius:50%;border:none;box-shadow:0 1px 3px 1px rgb(0 0 0/15%),0 1px 2px 0 rgb(0 0 0/30%);box-sizing:border-box;color:var(--sys-color-primary);font-family:var(--default-font-family);height:var(--width);justify-content:center;margin:0;max-height:var(--width);max-width:var(--width);min-height:var(--width);min-width:var(--width);overflow:hidden;padding:4px 3px 4px 5px;position:absolute;right:6px;display:none;width:var(--width)}.hover-button span{display:none;white-space:nowrap;padding-right:3px}.hover-button:focus,\n.hover-button:hover{border-radius:100px;max-width:200px;transition:max-width var(--sys-motion-duration-short4) var(--sys-motion-easing-emphasized);width:fit-content;gap:3px}.hover-button:focus-visible{outline:2px solid var(--sys-color-primary);outline-offset:2px}.hover-button devtools-icon{box-sizing:border-box;flex-shrink:0;height:16px;min-height:16px;min-width:16px;width:16px}.hover-button:focus span,\n.hover-button:hover span{display:block}.console-message-wrapper:not(.has-insight){&:hover,\n  &:focus,\n  &.console-selected{.hover-button{display:block;&:focus,\n      &:hover{display:inline-flex}}}}@media (forced-colors: active){.console-message-expand-icon,\n  .console-warning-level .expand-group-icon{forced-color-adjust:none;color:ButtonText}.console-message-wrapper:focus,\n  .console-message-wrapper:focus:last-of-type{forced-color-adjust:none;background-color:Highlight;border-top-color:Highlight;border-bottom-color:Highlight}.console-message-wrapper:focus *,\n  .console-message-wrapper:focus:last-of-type *,\n  .console-message-wrapper:focus .devtools-link,\n  .console-message-wrapper:focus:last-of-type .devtools-link{color:HighlightText!important}#console-messages .devtools-link,\n  #console-messages .devtools-link:hover{color:linktext}#console-messages .link:focus-visible,\n  #console-messages .devtools-link:focus-visible{background:Highlight;color:HighlightText}.console-message-wrapper:focus devtools-icon{color:HighlightText}.console-message-wrapper.console-error-level:focus,\n  .console-message-wrapper.console-error-level:focus:last-of-type{--override-error-text-color:HighlightText}}\n/*# sourceURL=consoleView.css */\n');var le=Object.freeze({__proto__:null,parseSourcePositionsFromErrorStack:oe,augmentErrorStackWithScriptIds:re});const ce={consoleclearWasPreventedDueTo:"`console.clear()` was prevented due to 'Preserve log'",consoleWasCleared:"Console was cleared",clearAllMessagesWithS:"Clear all messages with {PH1}",assertionFailed:"Assertion failed: ",violationS:"`[Violation]` {PH1}",interventionS:"`[Intervention]` {PH1}",deprecationS:"`[Deprecation]` {PH1}",thisValueWillNotBeCollectedUntil:"This value will not be collected until console is cleared.",thisValueWasEvaluatedUponFirst:"This value was evaluated upon first expanding. It may have changed since then.",functionWasResolvedFromBound:"Function was resolved from bound function.",exception:"<exception>",warning:"Warning",error:"Error",logpoint:"Logpoint",cndBreakpoint:"Conditional Breakpoint",repeatS:"{n, plural, =1 {Repeated # time} other {Repeated # times}}",warningS:"{n, plural, =1 {Warning, Repeated # time} other {Warning, Repeated # times}}",errorS:"{n, plural, =1 {Error, Repeated # time} other {Error, Repeated # times}}",url:"<URL>",tookNms:"took <N>ms",someEvent:"<some> event",Mxx:" M<XX>",attribute:"<attribute>",index:"(index)",value:"Value",console:"Console",stackMessageExpanded:"Stack table expanded",stackMessageCollapsed:"Stack table collapsed",explainThisError:"Understand this error",explainThisWarning:"Understand this warning",explainThisMessage:"Understand this message"},he=t.i18n.registerUIStrings("panels/console/ConsoleViewMessage.ts",ce),de=t.i18n.getLocalizedString.bind(void 0,he),ue=new WeakMap,pe=e=>ue.get(e),me=e=>t=>t instanceof n.RemoteObject.RemoteObject?t:e?"object"==typeof t?e.createRemoteObject(t):e.createRemoteObjectFromPrimitiveValue(t):n.RemoteObject.RemoteObject.fromLocalObject(t),ge="explain.console-message.hover",fe=new IntersectionObserver((e=>{for(const t of e)t.intersectionRatio>0&&p.userMetrics.actionTaken(p.UserMetrics.Action.InsightHoverButtonShown)}));class ve{message;linkifier;repeatCountInternal;closeGroupDecorationCount;consoleGroupInternal;selectableChildren;messageResized;elementInternal;consoleRowWrapper=null;previewFormatter;searchRegexInternal;messageIcon;traceExpanded;expandTrace;anchorElement;contentElementInternal;nestingLevelMarkers;searchHighlightNodes;searchHighlightNodeChanges;isVisibleInternal;cachedHeight;messagePrefix;timestampElement;inSimilarGroup;similarGroupMarker;lastInSimilarGroup;groupKeyInternal;repeatCountElement;requestResolver;issueResolver;#e=!1;#t=Promise.resolve();constructor(e,t,s,n,o){this.message=e,this.linkifier=t,this.requestResolver=s,this.issueResolver=n,this.repeatCountInternal=1,this.closeGroupDecorationCount=0,this.selectableChildren=[],this.messageResized=o,this.elementInternal=null,this.previewFormatter=new c.RemoteObjectPreviewFormatter.RemoteObjectPreviewFormatter,this.searchRegexInternal=null,this.messageIcon=null,this.traceExpanded=!1,this.expandTrace=null,this.anchorElement=null,this.contentElementInternal=null,this.nestingLevelMarkers=null,this.searchHighlightNodes=[],this.searchHighlightNodeChanges=[],this.isVisibleInternal=!1,this.cachedHeight=0,this.messagePrefix="",this.timestampElement=null,this.inSimilarGroup=!1,this.similarGroupMarker=null,this.lastInSimilarGroup=!1,this.groupKeyInternal="",this.repeatCountElement=null,this.consoleGroupInternal=null}setInsight(e){this.elementInternal?.querySelector("devtools-console-insight")?.remove(),this.elementInternal?.append(e),this.elementInternal?.classList.toggle("has-insight",!0),e.addEventListener("close",(()=>{p.userMetrics.actionTaken(p.UserMetrics.Action.InsightClosed),this.elementInternal?.classList.toggle("has-insight",!1),e.addEventListener("animationend",(()=>{this.elementInternal?.removeChild(e)}),{once:!0})}),{once:!0})}element(){return this.toMessageElement()}wasShown(){this.isVisibleInternal=!0}onResize(){}willHide(){this.isVisibleInternal=!1,this.cachedHeight=this.element().offsetHeight}isVisible(){return this.isVisibleInternal}fastHeight(){return this.cachedHeight?this.cachedHeight:this.approximateFastHeight()}approximateFastHeight(){return 19}consoleMessage(){return this.message}formatErrorStackPromiseForTest(){return this.#t}buildMessage(){let t,s=this.message.messageText;if(this.message.source===e.Console.FrontendMessageSource.ConsoleAPI)switch(this.message.type){case"trace":t=this.format(this.message.parameters||["console.trace"]);break;case"clear":t=document.createElement("span"),t.classList.add("console-info"),e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()?t.textContent=de(ce.consoleclearWasPreventedDueTo):t.textContent=de(ce.consoleWasCleared),o.Tooltip.Tooltip.install(t,de(ce.clearAllMessagesWithS,{PH1:String(o.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("console.clear"))}));break;case"dir":{const e=["%O",this.message.parameters?this.message.parameters[0]:void 0];t=this.format(e);break}case"profile":case"profileEnd":t=this.format([s]);break;default:{if("assert"===this.message.type&&(this.messagePrefix=de(ce.assertionFailed)),this.message.parameters&&1===this.message.parameters.length){const e=this.message.parameters[0];"string"!=typeof e&&"string"===e.type&&(t=this.tryFormatAsError(e.value))}const e=this.message.parameters||[s];t=t||this.format(e)}}else if("network"===this.message.source)t=this.formatAsNetworkRequest()||this.format([s]);else{const e=this.message.parameters&&s===this.message.parameters[0];"violation"===this.message.source?s=de(ce.violationS,{PH1:s}):"intervention"===this.message.source?s=de(ce.interventionS,{PH1:s}):"deprecation"===this.message.source&&(s=de(ce.deprecationS,{PH1:s}));const n=this.message.parameters||[s];e&&(n[0]=s),t=this.format(n)}t.classList.add("console-message-text");const n=document.createElement("span");if(n.classList.add("source-code"),this.anchorElement=this.buildMessageAnchor(),this.anchorElement&&n.appendChild(this.anchorElement),n.appendChild(t),"fusebox_preserve_log_rec"===this.message.context){const t=document.createElement("button");t.classList.add("devtools-link","text-button","link-style"),t.appendChild(t.ownerDocument.createTextNode("show settings")),t.addEventListener("click",(async()=>{await e.Revealer.reveal(e.Settings.Settings.instance().moduleSetting("preserve-console-log"))})),n.appendChild(t)}return n}formatAsNetworkRequest(){const e=g.NetworkLog.NetworkLog.requestForConsoleMessage(this.message);if(!e)return null;const t=document.createElement("span");if("error"===this.message.level){o.UIUtils.createTextChild(t,e.requestMethod+" ");const s=u.Linkifier.Linkifier.linkifyRevealable(e,e.url(),e.url(),void 0,void 0,"network-request");s.tabIndex=-1,this.selectableChildren.push({element:s,forceSelect:()=>s.focus()}),t.appendChild(s),e.failed&&o.UIUtils.createTextChildren(t," ",e.localizedFailDescription||""),0!==e.statusCode&&o.UIUtils.createTextChildren(t," ",String(e.statusCode));const n=e.getInferredStatusText();n&&o.UIUtils.createTextChildren(t," (",n,")")}else{const s=this.message.messageText,n=this.linkifyWithCustomLinkifier(s,((t,s,n,o)=>{const i=s===e.url()?u.Linkifier.Linkifier.linkifyRevealable(e,s,e.url(),void 0,void 0,"network-request"):u.Linkifier.Linkifier.linkifyURL(s,{text:t,lineNumber:n,columnNumber:o});return i.tabIndex=-1,this.selectableChildren.push({element:i,forceSelect:()=>i.focus()}),i}));t.appendChild(n)}return t}createAffectedResourceLinks(){const e=[],t=this.message.getAffectedResources()?.requestId;if(t){const s=new x.RequestLinkIcon.RequestLinkIcon;s.classList.add("resource-links"),s.data={affectedRequest:{requestId:t},requestResolver:this.requestResolver,displayURL:!1},e.push(s)}const s=this.message.getAffectedResources()?.issueId;if(s){const t=new b.IssueLinkIcon.IssueLinkIcon;t.classList.add("resource-links"),t.data={issueId:s,issueResolver:this.issueResolver},e.push(t)}return e}#s(){const e=g.NetworkLog.NetworkLog.requestForConsoleMessage(this.message);if(e?.resourceType().isStyleSheet())return p.UserMetrics.Action.StyleSheetInitiatorLinkClicked}buildMessageAnchor(){const e=this.message.runtimeModel();if(!e)return null;const t=(({stackFrameWithBreakpoint:t,scriptId:n,stackTrace:o,url:i,line:r,column:a})=>{const l=this.#s();return t?this.linkifier.maybeLinkifyConsoleCallFrame(e.target(),t,{inlineFrameIndex:0,revealBreakpoint:!0,userMetric:l}):n?this.linkifier.linkifyScriptLocation(e.target(),n,i||s.DevToolsPath.EmptyUrlString,r,{columnNumber:a,inlineFrameIndex:0,userMetric:l}):o&&o.callFrames.length?this.linkifier.linkifyStackTraceTopFrame(e.target(),o):i&&"undefined"!==i?this.linkifier.linkifyScriptLocation(e.target(),null,i,r,{columnNumber:a,inlineFrameIndex:0,userMetric:l}):null})(this.message);if(t){t.tabIndex=-1,this.selectableChildren.push({element:t,forceSelect:()=>t.focus()});const e=document.createElement("span");e.classList.add("console-message-anchor"),e.appendChild(t);for(const t of this.createAffectedResourceLinks())o.UIUtils.createTextChild(e," "),e.append(t);return o.UIUtils.createTextChild(e," "),e}return null}buildMessageWithStackTrace(t){const s=document.createElement("div");s.classList.add("console-message-stack-trace-toggle");const n=s.createChild("div","console-message-stack-trace-wrapper"),i=this.buildMessage(),r=d.Icon.create("triangle-right","console-message-expand-icon"),a=n.createChild("div");o.ARIAUtils.setExpanded(a,!1),a.appendChild(r),a.tabIndex=-1,a.appendChild(i);const l=n.createChild("div"),c=u.JSPresentationUtils.buildStackTracePreviewContents(t.target(),this.linkifier,{stackTrace:this.message.stackTrace,tabStops:void 0,widthConstrained:!0});l.appendChild(c.element);for(const e of c.links)this.selectableChildren.push({element:e,forceSelect:()=>e.focus()});l.classList.add("hidden"),o.ARIAUtils.setLabel(n,`${i.textContent} ${de(ce.stackMessageCollapsed)}`),o.ARIAUtils.markAsGroup(l);let h;this.expandTrace=e=>{e?h=window.setTimeout((()=>{p.userMetrics.actionTaken(p.UserMetrics.Action.TraceExpanded)}),300):clearTimeout(h),r.name=e?"triangle-down":"triangle-right",l.classList.toggle("hidden",!e);const t=de(e?ce.stackMessageExpanded:ce.stackMessageCollapsed);o.ARIAUtils.setLabel(n,`${i.textContent} ${t}`),o.ARIAUtils.alert(t),o.ARIAUtils.setExpanded(a,e),this.traceExpanded=e};return a.addEventListener("click",(e=>{o.UIUtils.isEditing()||n.hasSelection()||(this.expandTrace&&this.expandTrace(l.classList.contains("hidden")),e.consume())}),!1),"trace"===this.message.type&&e.Settings.Settings.instance().moduleSetting("console-trace-expand").get()&&this.expandTrace(!0),s._expandStackTraceForTest=this.expandTrace.bind(this,!0),s}format(e){const t=document.createElement("span");if(this.messagePrefix&&(t.createChild("span").textContent=this.messagePrefix),!e.length)return t;let s=e.map(me(this.message.runtimeModel()));const i="string"===n.RemoteObject.RemoteObject.type(s[0])&&(this.message.type!==n.ConsoleModel.FrontendMessageType.Result||"error"===this.message.level);i&&(s=this.formatWithSubstitutionString(s[0].description,s.slice(1),t),s.length&&o.UIUtils.createTextChild(t," "));for(let e=0;e<s.length;++e)i&&"string"===s[e].type?t.appendChild(this.linkifyStringAsFragment(s[e].description||"")):t.appendChild(this.formatParameter(s[e],!1,!0)),e<s.length-1&&o.UIUtils.createTextChild(t," ");return t}formatParameter(e,t,s){if(e.customPreview())return new c.CustomPreviewComponent.CustomPreviewComponent(e).element;const n=t?"object":e.subtype||e.type;let o;switch(n){case"error":o=this.formatParameterAsError(e);break;case"function":o=this.formatParameterAsFunction(e,s);break;case"array":case"arraybuffer":case"blob":case"dataview":case"generator":case"iterator":case"map":case"object":case"promise":case"proxy":case"set":case"typedarray":case"wasmvalue":case"weakmap":case"weakset":case"webassemblymemory":o=this.formatParameterAsObject(e,s);break;case"node":o=e.isNode()?this.formatParameterAsNode(e):this.formatParameterAsObject(e,!1);break;case"trustedtype":o=this.formatParameterAsObject(e,!1);break;case"string":o=this.formatParameterAsString(e);break;case"boolean":case"date":case"null":case"number":case"regexp":case"symbol":case"undefined":case"bigint":o=this.formatParameterAsValue(e);break;default:o=this.formatParameterAsValue(e),console.error(`Tried to format remote object of unknown type ${n}.`)}return o.classList.add(`object-value-${n}`),o.classList.add("source-code"),o}formatParameterAsValue(e){const t=document.createElement("span"),s=e.description||"";if(s.length>ke()){const e=new c.ObjectPropertiesSection.ExpandableTextPropertyValue(document.createElement("span"),s,Fe());t.appendChild(e.element)}else o.UIUtils.createTextChild(t,s);return t.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,e),!1),t}formatParameterAsTrustedType(e){const t=document.createElement("span"),s=document.createElement("span");return s.appendChild(this.formatParameterAsString(e)),s.classList.add("object-value-string"),o.UIUtils.createTextChild(t,`${e.className} `),t.appendChild(s),t}formatParameterAsObject(e,t){const s=document.createElement("span");if(s.classList.add("console-object"),t&&e.preview)s.classList.add("console-object-preview"),this.previewFormatter.appendObjectPreview(s,e.preview,!1),c.ObjectPropertiesSection.ObjectPropertiesSection.appendMemoryIcon(s,e);else if("function"===e.type){const t=s.createChild("span");c.ObjectPropertiesSection.ObjectPropertiesSection.formatObjectAsFunction(e,t,!1),s.classList.add("object-value-function")}else"trustedtype"===e.subtype?s.appendChild(this.formatParameterAsTrustedType(e)):o.UIUtils.createTextChild(s,e.description||"");if(!e.hasChildren||e.customPreview())return s;const i=s.createChild("span","object-state-note info-note");this.message.type===n.ConsoleModel.FrontendMessageType.QueryObjectResult?o.Tooltip.Tooltip.install(i,de(ce.thisValueWillNotBeCollectedUntil)):o.Tooltip.Tooltip.install(i,de(ce.thisValueWasEvaluatedUponFirst));const r=new c.ObjectPropertiesSection.ObjectPropertiesSection(e,s,this.linkifier);return r.element.classList.add("console-view-object-properties-section"),r.enableContextMenu(),r.setShowSelectionOnKeyboardFocus(!0,!0),this.selectableChildren.push(r),r.addEventListener(o.TreeOutline.Events.ElementAttached,this.messageResized),r.addEventListener(o.TreeOutline.Events.ElementExpanded,this.messageResized),r.addEventListener(o.TreeOutline.Events.ElementCollapsed,this.messageResized),r.element}formatParameterAsFunction(e,t){const s=document.createElement("span");return n.RemoteObject.RemoteFunction.objectAsFunction(e).targetFunction().then(function(n){const i=document.createElement("span"),r=c.ObjectPropertiesSection.ObjectPropertiesSection.formatObjectAsFunction(n,i,!0,t);if(s.appendChild(i),n!==e){const e=s.createChild("span","object-state-note info-note");o.Tooltip.Tooltip.install(e,de(ce.functionWasResolvedFromBound))}s.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,e),!1),r.then((()=>this.formattedParameterAsFunctionForTest()))}.bind(this)),s}formattedParameterAsFunctionForTest(){}contextMenuEventFired(e,t){const s=new o.ContextMenu.ContextMenu(t);s.appendApplicableItems(e),s.show()}renderPropertyPreviewOrAccessor(e,t,s){return"accessor"===t.type?this.formatAsAccessorProperty(e,s.map((e=>e.name.toString())),!1):this.previewFormatter.renderPropertyPreview(t.type,"subtype"in t?t.subtype:void 0,null,t.value)}formatParameterAsNode(e){const t=document.createElement("span"),s=e.runtimeModel().target().model(n.DOMModel.DOMModel);return s?(s.pushObjectAsNodeToFrontend(e).then((async s=>{if(!s)return void t.appendChild(this.formatParameterAsObject(e,!1));const n=await o.UIUtils.Renderer.render(s);n?(n.tree&&(this.selectableChildren.push(n.tree),n.tree.addEventListener(o.TreeOutline.Events.ElementAttached,this.messageResized),n.tree.addEventListener(o.TreeOutline.Events.ElementExpanded,this.messageResized),n.tree.addEventListener(o.TreeOutline.Events.ElementCollapsed,this.messageResized)),t.appendChild(n.node)):t.appendChild(this.formatParameterAsObject(e,!1)),this.formattedParameterAsNodeForTest()})),t):t}formattedParameterAsNodeForTest(){}formatParameterAsString(e){const t=e.description??"",n=s.StringUtilities.formatAsJSLiteral(t),o=document.createElement("span");return o.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,e),!1),o.appendChild(this.linkifyStringAsFragment(n)),o}formatParameterAsError(e){const t=document.createElement("span"),s=async(e,o)=>{const i=n.RemoteObject.RemoteError.objectAsError(e),[r,a]=await Promise.all([i.exceptionDetails(),i.cause()]),l=o?"div":"span",c=this.tryFormatAsError(i.errorStack,r,l)??this.linkifyStringAsFragment(i.errorStack);o&&c.prepend("Caused by: "),t.appendChild(c),a&&"error"===a.subtype?await s(a,!0):a&&"string"===a.type&&t.append(`Caused by: ${a.value}`)};return this.#t=s(e,!1),t}formatAsArrayEntry(e){return this.previewFormatter.renderPropertyPreview(e.type,e.subtype,e.className,e.description)}formatAsAccessorProperty(e,t,n){const i=c.ObjectPropertiesSection.ObjectPropertyTreeElement.createRemoteObjectAccessorPropertySpan(e,t,function(e){const t=e.wasThrown,r=e.object;if(!r)return;if(i.removeChildren(),t){const e=i.createChild("span");e.textContent=de(ce.exception),o.Tooltip.Tooltip.install(e,r.description)}else if(n)i.appendChild(this.formatAsArrayEntry(r));else{const e=100,t=r.type,n=r.subtype;let o="";"function"!==t&&r.description&&(o="string"===t||"regexp"===n||"trustedtype"===n?s.StringUtilities.trimMiddle(r.description,e):s.StringUtilities.trimEndWithMaxLength(r.description,e)),i.appendChild(this.previewFormatter.renderPropertyPreview(t,n,r.className,o))}}.bind(this));return i}formatWithSubstitutionString(e,t,s){const n=new Map,{tokens:o,args:i}=B(e,t);for(const e of o)switch(e.type){case"generic":s.append(this.formatParameter(e.value,!0,!1));break;case"optimal":s.append(this.formatParameter(e.value,!1,!0));break;case"string":if(0===n.size)s.append(this.linkifyStringAsFragment(e.value));else{const t=e.value.split("\n");for(let e=0;e<t.length;e++){e>0&&s.append(document.createElement("br"));const o=document.createElement("span");o.style.setProperty("contain","paint"),o.style.setProperty("display","inline-block"),o.style.setProperty("max-width","100%"),o.appendChild(this.linkifyStringAsFragment(t[e]));for(const[e,{value:t,priority:s}]of n)o.style.setProperty(e,t,s);s.append(o)}}break;case"style":H(n,e.value)}return i}matchesFilterRegex(e){e.lastIndex=0;const t=this.contentElement(),s=this.anchorElement?this.anchorElement.deepTextContent():"";return Boolean(s)&&e.test(s.trim())||e.test(t.deepTextContent().slice(s.length))}matchesFilterText(e){return this.contentElement().deepTextContent().toLowerCase().includes(e.toLowerCase())}updateTimestamp(){this.contentElementInternal&&(e.Settings.Settings.instance().moduleSetting("console-timestamps-enabled").get()?(this.timestampElement||(this.timestampElement=document.createElement("span"),this.timestampElement.classList.add("console-timestamp")),this.timestampElement.textContent=o.UIUtils.formatTimestamp(this.message.timestamp,!1)+" ",o.Tooltip.Tooltip.install(this.timestampElement,o.UIUtils.formatTimestamp(this.message.timestamp,!0)),this.contentElementInternal.insertBefore(this.timestampElement,this.contentElementInternal.firstChild)):this.timestampElement&&(this.timestampElement.remove(),this.timestampElement=null))}nestingLevel(){let e=0;for(let t=this.consoleGroup();null!==t;t=t.consoleGroup())e++;return e}setConsoleGroup(e){this.consoleGroupInternal=e}clearConsoleGroup(){this.consoleGroupInternal=null}consoleGroup(){return this.consoleGroupInternal}setInSimilarGroup(e,t){this.inSimilarGroup=e,this.lastInSimilarGroup=e&&Boolean(t),this.similarGroupMarker&&!e?(this.similarGroupMarker.remove(),this.similarGroupMarker=null):this.elementInternal&&!this.similarGroupMarker&&e&&(this.similarGroupMarker=document.createElement("div"),this.similarGroupMarker.classList.add("nesting-level-marker"),this.consoleRowWrapper?.insertBefore(this.similarGroupMarker,this.consoleRowWrapper.firstChild),this.similarGroupMarker.classList.toggle("group-closed",this.lastInSimilarGroup))}isLastInSimilarGroup(){return Boolean(this.inSimilarGroup)&&Boolean(this.lastInSimilarGroup)}resetCloseGroupDecorationCount(){this.closeGroupDecorationCount&&(this.closeGroupDecorationCount=0,this.updateCloseGroupDecorations())}incrementCloseGroupDecorationCount(){++this.closeGroupDecorationCount,this.updateCloseGroupDecorations()}updateCloseGroupDecorations(){if(this.nestingLevelMarkers)for(let e=0,t=this.nestingLevelMarkers.length;e<t;++e){this.nestingLevelMarkers[e].classList.toggle("group-closed",t-e<=this.closeGroupDecorationCount)}}focusedChildIndex(){return this.selectableChildren.length?this.selectableChildren.findIndex((e=>e.element.hasFocus())):-1}onKeyDown(e){!o.UIUtils.isEditing()&&this.elementInternal&&this.elementInternal.hasFocus()&&!this.elementInternal.hasSelection()&&this.maybeHandleOnKeyDown(e)&&e.consume(!0)}maybeHandleOnKeyDown(e){const t=this.focusedChildIndex(),s=-1===t;if(this.expandTrace&&s&&("ArrowLeft"===e.key&&this.traceExpanded||"ArrowRight"===e.key&&!this.traceExpanded))return this.expandTrace(!this.traceExpanded),!0;if(!this.selectableChildren.length)return!1;if("ArrowLeft"===e.key)return this.elementInternal&&this.elementInternal.focus(),!0;if("ArrowRight"===e.key&&s&&this.selectNearestVisibleChild(0))return!0;if("ArrowUp"===e.key){const e=this.nearestVisibleChild(0);if(this.selectableChildren[t]===e&&e)return this.elementInternal&&this.elementInternal.focus(),!0;if(this.selectNearestVisibleChild(t-1,!0))return!0}if("ArrowDown"===e.key){if(s&&this.selectNearestVisibleChild(0))return!0;if(!s&&this.selectNearestVisibleChild(t+1))return!0}return!1}selectNearestVisibleChild(e,t){const s=this.nearestVisibleChild(e,t);return!!s&&(s.forceSelect(),!0)}nearestVisibleChild(e,t){const s=this.selectableChildren.length;if(e<0||e>=s)return null;const n=t?-1:1;let o=e;for(;!this.selectableChildren[o].element.offsetParent;)if(o+=n,o<0||o>=s)return null;return this.selectableChildren[o]}focusLastChildOrSelf(){this.elementInternal&&!this.selectNearestVisibleChild(this.selectableChildren.length-1,!0)&&this.elementInternal.focus()}setContentElement(e){console.assert(!this.contentElementInternal,"Cannot set content element twice"),this.contentElementInternal=e}getContentElement(){return this.contentElementInternal}contentElement(){if(this.contentElementInternal)return this.contentElementInternal;const e=document.createElement("div");e.classList.add("console-message"),this.messageIcon&&e.appendChild(this.messageIcon),this.contentElementInternal=e;const t=this.message.runtimeModel();let s;const n=Boolean(this.message.stackTrace)&&("network"===this.message.source||"violation"===this.message.source||"error"===this.message.level||"warning"===this.message.level||"trace"===this.message.type);return s=t&&n?this.buildMessageWithStackTrace(t):this.buildMessage(),e.appendChild(s),this.updateTimestamp(),this.contentElementInternal}toMessageElement(){return this.elementInternal||(this.elementInternal=document.createElement("div"),this.elementInternal.tabIndex=-1,this.elementInternal.addEventListener("keydown",this.onKeyDown.bind(this)),this.updateMessageElement(),this.elementInternal.classList.toggle("console-adjacent-user-command-result",this.#e)),this.elementInternal}updateMessageElement(){if(this.elementInternal){this.elementInternal.className="console-message-wrapper",this.elementInternal.setAttribute("jslog",`${h.item("console-message").track({click:!0,keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter|Space|Home|End"})}`),this.elementInternal.removeChildren(),this.consoleRowWrapper=this.elementInternal.createChild("div"),this.consoleRowWrapper.classList.add("console-row-wrapper"),this.message.isGroupStartMessage()&&this.elementInternal.classList.add("console-group-title"),this.message.source===e.Console.FrontendMessageSource.ConsoleAPI&&this.elementInternal.classList.add("console-from-api"),this.inSimilarGroup&&(this.similarGroupMarker=this.consoleRowWrapper.createChild("div","nesting-level-marker"),this.similarGroupMarker.classList.toggle("group-closed",this.lastInSimilarGroup)),this.nestingLevelMarkers=[];for(let e=0;e<this.nestingLevel();++e)this.nestingLevelMarkers.push(this.consoleRowWrapper.createChild("div","nesting-level-marker"));switch(this.updateCloseGroupDecorations(),ue.set(this.elementInternal,this),this.message.level){case"verbose":this.elementInternal.classList.add("console-verbose-level"),o.ARIAUtils.setLabel(this.elementInternal,this.text);break;case"info":this.elementInternal.classList.add("console-info-level"),this.message.type===n.ConsoleModel.FrontendMessageType.System&&this.elementInternal.classList.add("console-system-type"),o.ARIAUtils.setLabel(this.elementInternal,this.text);break;case"warning":this.elementInternal.classList.add("console-warning-level"),this.elementInternal.role="log",o.ARIAUtils.setLabel(this.elementInternal,this.text);break;case"error":this.elementInternal.classList.add("console-error-level"),this.elementInternal.role="log",o.ARIAUtils.setLabel(this.elementInternal,this.text)}this.updateMessageIcon(),this.shouldRenderAsWarning()&&this.elementInternal.classList.add("console-warning-level"),this.consoleRowWrapper.appendChild(this.contentElement()),o.ActionRegistry.ActionRegistry.instance().hasAction(ge)&&this.shouldShowInsights()&&(p.userMetrics.actionTaken(p.UserMetrics.Action.InsightConsoleMessageShown),this.consoleRowWrapper.append(this.#n())),this.repeatCountInternal>1&&this.showRepeatCountElement()}}shouldShowInsights(){return(this.message.source!==e.Console.FrontendMessageSource.ConsoleAPI||""!==this.message.stackTrace?.callFrames[0]?.url)&&(""!==this.message.messageText&&this.message.source!==e.Console.FrontendMessageSource.SelfXss&&("error"===this.message.level||"warning"===this.message.level))}getExplainLabel(){return"error"===this.message.level?de(ce.explainThisError):"warning"===this.message.level?de(ce.explainThisWarning):de(ce.explainThisMessage)}getExplainActionId(){return"error"===this.message.level?"explain.console-message.context.error":"warning"===this.message.level?"explain.console-message.context.warning":"explain.console-message.context.other"}#n(){const e=new d.Icon.Icon;e.data={iconName:"lightbulb-spark",color:"var(--sys-color-primary)",width:"16px",height:"16px"};const t=document.createElement("button");t.append(e),t.onclick=e=>{e.stopPropagation(),o.Context.Context.instance().setFlavor(ve,this);o.ActionRegistry.ActionRegistry.instance().getAction(ge).execute()};const s=document.createElement("span");return s.innerText=this.getExplainLabel(),t.append(s),t.classList.add("hover-button"),t.ariaLabel=this.getExplainLabel(),t.tabIndex=0,t.setAttribute("jslog",`${h.action(ge).track({click:!0})}`),fe.observe(t),t}shouldRenderAsWarning(){return!("verbose"!==this.message.level&&"info"!==this.message.level||"violation"!==this.message.source&&"deprecation"!==this.message.source&&"intervention"!==this.message.source&&"recommendation"!==this.message.source)}updateMessageIcon(){this.messageIcon&&(this.messageIcon.remove(),this.messageIcon=null);let e="",t="",s="";"warning"===this.message.level?(e="var(--icon-warning)",t="warning-filled",s=de(ce.warning)):"error"===this.message.level?(e="var(--icon-error)",t="cross-circle-filled",s=de(ce.error)):this.message.originatesFromLogpoint?(t="console-logpoint",s=de(ce.logpoint)):this.message.originatesFromConditionalBreakpoint&&(t="console-conditional-breakpoint",s=de(ce.cndBreakpoint)),t&&(this.messageIcon=new d.Icon.Icon,this.messageIcon.data={iconName:t,color:e,width:"14px",height:"14px"},this.messageIcon.classList.add("message-level-icon"),this.contentElementInternal&&this.contentElementInternal.insertBefore(this.messageIcon,this.contentElementInternal.firstChild),o.ARIAUtils.setLabel(this.messageIcon,s))}setAdjacentUserCommandResult(e){this.#e=e,this.elementInternal?.classList.toggle("console-adjacent-user-command-result",this.#e)}repeatCount(){return this.repeatCountInternal||1}resetIncrementRepeatCount(){this.repeatCountInternal=1,this.repeatCountElement&&(this.repeatCountElement.remove(),this.contentElementInternal&&this.contentElementInternal.classList.remove("repeated-message"),this.repeatCountElement=null)}incrementRepeatCount(){this.repeatCountInternal++,this.showRepeatCountElement()}setRepeatCount(e){this.repeatCountInternal=e,this.showRepeatCountElement()}showRepeatCountElement(){if(!this.elementInternal)return;if(!this.repeatCountElement){switch(this.repeatCountElement=document.createElement("span",{is:"dt-small-bubble"}),this.repeatCountElement.classList.add("console-message-repeat-count"),this.message.level){case"warning":this.repeatCountElement.type="warning";break;case"error":this.repeatCountElement.type="error";break;case"verbose":this.repeatCountElement.type="verbose";break;default:this.repeatCountElement.type="info"}this.shouldRenderAsWarning()&&(this.repeatCountElement.type="warning"),this.consoleRowWrapper?.insertBefore(this.repeatCountElement,this.contentElementInternal),this.contentElement().classList.add("repeated-message")}let e;this.repeatCountElement.textContent=`${this.repeatCountInternal}`,e="warning"===this.message.level?de(ce.warningS,{n:this.repeatCountInternal}):"error"===this.message.level?de(ce.errorS,{n:this.repeatCountInternal}):de(ce.repeatS,{n:this.repeatCountInternal}),o.ARIAUtils.setLabel(this.repeatCountElement,e)}get text(){return this.message.messageText}toExportString(){const e=[],t=this.contentElement().childTextNodes().map(u.Linkifier.Linkifier.untruncatedNodeText).join("");for(let s=0;s<this.repeatCount();++s)e.push(t);return e.join("\n")}toMessageTextString(){const e=this.contentElement().querySelector(".console-message-text");return e?e.deepTextContent().trim():this.consoleMessage().messageText}setSearchRegex(e){if(this.searchHighlightNodeChanges&&this.searchHighlightNodeChanges.length&&o.UIUtils.revertDomChanges(this.searchHighlightNodeChanges),this.searchRegexInternal=e,this.searchHighlightNodes=[],this.searchHighlightNodeChanges=[],!this.searchRegexInternal)return;const t=this.contentElement().deepTextContent();let s;this.searchRegexInternal.lastIndex=0;const n=[];for(;(s=this.searchRegexInternal.exec(t))&&s[0];)n.push(new i.TextRange.SourceRange(s.index,s[0].length));n.length&&(this.searchHighlightNodes=o.UIUtils.highlightSearchResults(this.contentElement(),n,this.searchHighlightNodeChanges))}searchRegex(){return this.searchRegexInternal}searchCount(){return this.searchHighlightNodes.length}searchHighlightNode(e){return this.searchHighlightNodes[e]}async getInlineFrames(e,t,s,o){const i=m.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),r=f.Workspace.WorkspaceImpl.instance().projects().map((e=>e.uiSourceCodeForURL(t))).flat().filter((e=>Boolean(e))).map((e=>i.scriptsForUISourceCode(e))).flat();if(r.length){const t=new n.DebuggerModel.Location(e,r[0].scriptId,s||0,o),a=await i.pluginManager.getFunctionInfo(r[0],t);return a&&"frames"in a?a:{frames:[]}}return{frames:[]}}async expandInlineStackFrames(e,t,s,n,o,i,r,a){const{frames:l}=await this.getInlineFrames(e,n,o,i);if(!l.length)return!1;for(let c=0;c<l.length;++c){const{name:h}=l[c],d=document.createElement("span");d.appendChild(this.linkifyStringAsFragment(`${t} ${h} (`));const u=this.linkifier.linkifyScriptLocation(e.target(),null,n,o,{columnNumber:i,inlineFrameIndex:c});u.tabIndex=-1,this.selectableChildren.push({element:u,forceSelect:()=>u.focus()}),d.appendChild(u),d.appendChild(this.linkifyStringAsFragment(s)),r.insertBefore(d,a)}return!0}createScriptLocationLinkForSyntaxError(e,t){const{scriptId:s,lineNumber:n,columnNumber:o}=t;if(!s)return;const i=t.url||e.scriptForId(s)?.sourceURL;if(!i)return;const r=this.linkifier.linkifyScriptLocation(e.target(),t.scriptId||null,i,n,{columnNumber:o,inlineFrameIndex:0,showColumnNumber:!0});return r.tabIndex=-1,r}tryFormatAsError(e,t,s="span"){const n=this.message.runtimeModel();if(!n)return null;const o=oe(n,e);if(!o?.length)return null;t?.stackTrace&&re(o,t.stackTrace);const i=n.debuggerModel(),r=document.createElement(s);for(let e=0;e<o.length;++e){const s=e<o.length-1?"\n":"",{line:n,link:a}=o[e];if(!a&&t&&n.startsWith("SyntaxError")){r.appendChild(this.linkifyStringAsFragment(n));const e=this.createScriptLocationLinkForSyntaxError(i,t);e&&(r.append(" (at "),r.appendChild(e),r.append(")")),r.append(s);continue}if(!a){r.appendChild(this.linkifyStringAsFragment(`${n}${s}`));continue}const l=document.createElement("span"),c=`${a.suffix}${s}`;l.appendChild(this.linkifyStringAsFragment(a.prefix));const h=this.linkifier.linkifyScriptLocation(i.target(),a.scriptId||null,a.url,a.lineNumber,{columnNumber:a.columnNumber,inlineFrameIndex:0,showColumnNumber:!0});if(h.tabIndex=-1,this.selectableChildren.push({element:h,forceSelect:()=>h.focus()}),l.appendChild(h),l.appendChild(this.linkifyStringAsFragment(c)),r.appendChild(l),!a.enclosedInBraces)continue;const d=a.prefix.substring(0,a.prefix.lastIndexOf(" ",a.prefix.length-3)),u=this.selectableChildren.length-1;this.expandInlineStackFrames(i,d,c,a.url,a.lineNumber,a.columnNumber,r,l).then((e=>{e&&(r.removeChild(l),this.selectableChildren.splice(u,1))}))}return r}linkifyWithCustomLinkifier(t,n){if(t.length>ke()){const e=new c.ObjectPropertiesSection.ExpandableTextPropertyValue(document.createElement("span"),t,Fe()),s=document.createDocumentFragment();return s.appendChild(e.element),s}const o=document.createDocumentFragment(),i=ve.tokenizeMessageText(t);let r=!1;for(const t of i)if(t.text)switch(r&&(t.text=`blob:${t.text}`,r=!r),"'blob:"===t.text&&t===i[0]&&(r=!0,t.text="'"),t.type){case"url":{const i=t.text.startsWith("www.")?"http://"+t.text:t.text,r=e.ParsedURL.ParsedURL.splitLineAndColumn(i),a=e.ParsedURL.ParsedURL.removeWasmFunctionInfoFromURL(r.url);let l;l=r?n(t.text,a,r.lineNumber,r.columnNumber):n(t.text,s.DevToolsPath.EmptyUrlString),o.appendChild(l);break}default:o.appendChild(document.createTextNode(t.text))}return o}linkifyStringAsFragment(e){return this.linkifyWithCustomLinkifier(e,((e,t,s,n)=>{const o={text:e,lineNumber:s,columnNumber:n},i=u.Linkifier.Linkifier.linkifyURL(t,o);return i.tabIndex=-1,this.selectableChildren.push({element:i,forceSelect:()=>i.focus()}),i}))}static tokenizeMessageText(e){const{tokenizerRegexes:t,tokenizerTypes:s}=Ce();if(e.length>ke())return[{text:e,type:void 0}];return i.TextUtils.Utils.splitStringByRegexes(e,t).map((e=>({text:e.value,type:s[e.regexIndex]})))}groupKey(){return this.groupKeyInternal||(this.groupKeyInternal=this.message.groupCategoryKey()+":"+this.groupTitle()),this.groupKeyInternal}groupTitle(){return ve.tokenizeMessageText(this.message.messageText).reduce(((e,t)=>{let s=t.text;return"url"===t.type?s=de(ce.url):"time"===t.type?s=de(ce.tookNms):"event"===t.type?s=de(ce.someEvent):"milestone"===t.type?s=de(ce.Mxx):"autofill"===t.type&&(s=de(ce.attribute)),e+s}),"").replace(/[%]o/g,"")}}let be=null,xe=null;function Ce(){if(!be||!xe){const e="\\u0000-\\u0020\\u007f-\\u009f",t=new RegExp("(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|data:|www\\.)[^\\s"+e+'"]{2,}[^\\s'+e+"\"')}\\],:;.!?]","u"),s=/(?:\/[\w\.-]*)+\:[\d]+/,n=/took [\d]+ms/,o=/'\w+' event/,i=/\sM[6-7]\d/,r=/\(suggested: \"[\w-]+\"\)/,a=new Map;return a.set(t,"url"),a.set(s,"url"),a.set(n,"time"),a.set(o,"event"),a.set(i,"milestone"),a.set(r,"autofill"),be=Array.from(a.keys()),xe=Array.from(a.values()),{tokenizerRegexes:be,tokenizerTypes:xe}}return{tokenizerRegexes:be,tokenizerTypes:xe}}class we extends ve{collapsedInternal;expandGroupIcon;onToggle;groupEndMessageInternal;constructor(e,t,s,n,o,i){console.assert(e.isGroupStartMessage()),super(e,t,s,n,i),this.collapsedInternal="startGroupCollapsed"===e.type,this.expandGroupIcon=null,this.onToggle=o,this.groupEndMessageInternal=null}setCollapsed(e){this.collapsedInternal=e,this.expandGroupIcon&&(this.expandGroupIcon.name=this.collapsedInternal?"triangle-right":"triangle-down"),this.onToggle.call(null)}collapsed(){return this.collapsedInternal}maybeHandleOnKeyDown(e){return-1===this.focusedChildIndex()&&("ArrowLeft"===e.key&&!this.collapsedInternal||"ArrowRight"===e.key&&this.collapsedInternal)?(this.setCollapsed(!this.collapsedInternal),!0):super.maybeHandleOnKeyDown(e)}toMessageElement(){let e=this.elementInternal||null;if(!e){e=super.toMessageElement();const t=this.collapsedInternal?"triangle-right":"triangle-down";this.expandGroupIcon=d.Icon.create(t,"expand-group-icon"),this.contentElement().tabIndex=-1,this.repeatCountElement?this.repeatCountElement.insertBefore(this.expandGroupIcon,this.repeatCountElement.firstChild):this.consoleRowWrapper?.insertBefore(this.expandGroupIcon,this.contentElementInternal),e.addEventListener("click",(()=>this.setCollapsed(!this.collapsedInternal)))}return e}showRepeatCountElement(){super.showRepeatCountElement(),this.repeatCountElement&&this.expandGroupIcon&&this.repeatCountElement.insertBefore(this.expandGroupIcon,this.repeatCountElement.firstChild)}messagesHidden(){if(this.collapsed())return!0;const e=this.consoleGroup();return Boolean(e&&e.messagesHidden())}setGroupEnd(e){if("endGroup"!==e.consoleMessage().type)throw new Error("Invalid console message as group end");if(null!==this.groupEndMessageInternal)throw new Error("Console group already has an end");this.groupEndMessageInternal=e}groupEnd(){return this.groupEndMessageInternal}}class Se extends ve{formattedCommand;constructor(e,t,s,n,o){super(e,t,s,n,o),this.formattedCommand=null}contentElement(){const e=this.getContentElement();if(e)return e;const t=document.createElement("div");this.setContentElement(t),t.classList.add("console-user-command");const n=new d.Icon.Icon;return n.data={iconName:"chevron-right",color:"var(--icon-default)",width:"16px",height:"16px"},n.classList.add("command-result-icon"),t.appendChild(n),ue.set(t,this),this.formattedCommand=document.createElement("span"),this.formattedCommand.classList.add("source-code"),this.formattedCommand.textContent=s.StringUtilities.replaceControlCharacters(this.text),t.appendChild(this.formattedCommand),this.formattedCommand.textContent.length<Me?v.CodeHighlighter.highlightNode(this.formattedCommand,"text/javascript").then(this.updateSearch.bind(this)):this.updateSearch(),this.updateTimestamp(),t}updateSearch(){this.setSearchRegex(this.searchRegex())}}class Ie extends ve{contentElement(){const e=super.contentElement();if(!e.classList.contains("console-user-command-result")&&(e.classList.add("console-user-command-result"),"info"===this.consoleMessage().level)){const t=new d.Icon.Icon;t.data={iconName:"chevron-left-dot",color:"var(--icon-default)",width:"16px",height:"16px"},t.classList.add("command-result-icon"),e.insertBefore(t,e.firstChild)}return e}}class ye extends ve{dataGrid;constructor(e,t,s,n,o){super(e,t,s,n,o),console.assert("table"===e.type),this.dataGrid=null}wasShown(){this.dataGrid&&this.dataGrid.updateWidths(),super.wasShown()}onResize(){this.isVisible()&&this.dataGrid&&this.dataGrid.onResize()}contentElement(){const e=this.getContentElement();if(e)return e;const t=document.createElement("div");return t.classList.add("console-message"),this.messageIcon&&t.appendChild(this.messageIcon),this.setContentElement(t),t.appendChild(this.buildTableMessage()),this.updateTimestamp(),t}buildTableMessage(){const e=document.createElement("span");e.classList.add("source-code"),this.anchorElement=this.buildMessageAnchor(),this.anchorElement&&e.appendChild(this.anchorElement);const t=this.message.parameters&&this.message.parameters.length?this.message.parameters[0]:null;if(!t)return this.buildMessage();const s=me(this.message.runtimeModel())(t);if(!s||!s.preview)return this.buildMessage();const n=Symbol("rawValueColumn"),o=[],i=s.preview,r=[];for(let e=0;e<i.properties.length;++e){const t=i.properties[e];let a;if(t.valuePreview&&t.valuePreview.properties.length)a=t.valuePreview.properties;else{if(!t.value&&""!==t.value)continue;a=[{name:n,type:t.type,value:t.value}]}const l=new Map,c=20;for(let e=0;e<a.length;++e){const n=a[e];let i=-1!==o.indexOf(n.name);if(!i){if(o.length===c)continue;i=!0,o.push(n.name)}if(i){const e=this.renderPropertyPreviewOrAccessor(s,n,[t,n]);e.classList.add("console-message-nowrap-below"),l.set(n.name,e)}}r.push({rowName:t.name,rowValue:l})}const a=[];for(const{rowName:e,rowValue:t}of r){a.push(e);for(let e=0;e<o.length;++e)a.push(t.get(o[e]))}o.unshift(de(ce.index));const l=o.map((e=>e===n?de(ce.value):e.toString()));if(a.length&&(this.dataGrid=C.SortableDataGrid.SortableDataGrid.create(l,a,de(ce.console)),this.dataGrid)){this.dataGrid.setStriped(!0),this.dataGrid.setFocusable(!1);const t=document.createElement("span");t.classList.add("console-message-text");const n=t.createChild("div","console-message-formatted-table"),o=n.createChild("span");n.appendChild(this.formatParameter(s,!0,!1));const i=o.attachShadow({mode:"open"}),r=this.dataGrid.asWidget();r.markAsRoot(),r.show(i),r.registerCSSFiles([ne,O]),e.appendChild(t),this.dataGrid.renderInline()}return e}approximateFastHeight(){const e=this.message.parameters&&this.message.parameters[0];return e&&"string"!=typeof e&&e.preview?19*e.preview.properties.length:19}}const Me=1e4;let Ee=1e4,Te=5e3;const ke=()=>Ee,Fe=()=>Te;var Le=Object.freeze({__proto__:null,getMessageForElement:pe,ConsoleViewMessage:ve,ConsoleGroupViewMessage:we,ConsoleCommand:Se,ConsoleCommandResult:Ie,ConsoleTableMessageView:ye,MaxLengthForLinks:40,getMaxTokenizableStringLength:ke,setMaxTokenizableStringLength:e=>{Ee=e},getLongStringVisibleLength:Fe,setLongStringVisibleLength:e=>{Te=e}});class Re{element;topGapElement;topGapElementActive;contentElementInternal;bottomGapElement;bottomGapElementActive;provider;virtualSelectedIndex;firstActiveIndex;lastActiveIndex;renderedItems;anchorSelection;headSelection;itemCount;cumulativeHeights;muteCopyHandler;observer;observerConfig;stickToBottomInternal;selectionIsBackward;lastSelectedElement;cachedProviderElements;constructor(e){this.element=document.createElement("div"),this.element.style.overflow="auto",this.topGapElement=this.element.createChild("div"),this.topGapElement.style.height="0px",this.topGapElement.style.color="transparent",this.topGapElementActive=!1,this.contentElementInternal=this.element.createChild("div"),this.bottomGapElement=this.element.createChild("div"),this.bottomGapElement.style.height="0px",this.bottomGapElement.style.color="transparent",this.bottomGapElementActive=!1,this.topGapElement.textContent="\ufeff",this.bottomGapElement.textContent="\ufeff",o.ARIAUtils.markAsHidden(this.topGapElement),o.ARIAUtils.markAsHidden(this.bottomGapElement),this.provider=e,this.element.addEventListener("scroll",this.onScroll.bind(this),!1),this.element.addEventListener("copy",this.onCopy.bind(this),!1),this.element.addEventListener("dragstart",this.onDragStart.bind(this),!1),this.contentElementInternal.addEventListener("focusin",this.onFocusIn.bind(this),!1),this.contentElementInternal.addEventListener("focusout",this.onFocusOut.bind(this),!1),this.contentElementInternal.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.virtualSelectedIndex=-1,this.contentElementInternal.tabIndex=-1,this.firstActiveIndex=-1,this.lastActiveIndex=-1,this.renderedItems=[],this.anchorSelection=null,this.headSelection=null,this.itemCount=0,this.cumulativeHeights=new Int32Array(0),this.muteCopyHandler=!1,this.observer=new MutationObserver(this.refresh.bind(this)),this.observerConfig={childList:!0,subtree:!0},this.stickToBottomInternal=!1,this.selectionIsBackward=!1}stickToBottom(){return this.stickToBottomInternal}setStickToBottom(e){this.stickToBottomInternal=e,this.stickToBottomInternal?this.observer.observe(this.contentElementInternal,this.observerConfig):this.observer.disconnect()}hasVirtualSelection(){return-1!==this.virtualSelectedIndex}copyWithStyles(){this.muteCopyHandler=!0,this.element.ownerDocument.execCommand("copy"),this.muteCopyHandler=!1}onCopy(e){if(this.muteCopyHandler)return;const t=this.selectedText();t&&(e.preventDefault(),this.selectionContainsTable()?this.copyWithStyles():e.clipboardData&&e.clipboardData.setData("text/plain",t))}onFocusIn(e){const t=this.renderedItems.findIndex((t=>t.element().isSelfOrAncestor(e.target)));-1!==t&&(this.virtualSelectedIndex=this.firstActiveIndex+t);let s=!1;-1===this.virtualSelectedIndex&&this.isOutsideViewport(e.relatedTarget)&&e.target===this.contentElementInternal&&this.itemCount&&(s=!0,this.virtualSelectedIndex=this.itemCount-1,this.refresh(),this.scrollItemIntoView(this.virtualSelectedIndex)),this.updateFocusedItem(s)}onFocusOut(e){this.isOutsideViewport(e.relatedTarget)&&(this.virtualSelectedIndex=-1),this.updateFocusedItem()}isOutsideViewport(e){return null!==e&&!e.isSelfOrDescendant(this.contentElementInternal)}onDragStart(e){const t=this.selectedText();return!!t&&(e.dataTransfer&&(e.dataTransfer.clearData(),e.dataTransfer.setData("text/plain",t),e.dataTransfer.effectAllowed="copy"),!0)}onKeyDown(e){if(o.UIUtils.isEditing()||!this.itemCount||e.shiftKey)return;let t=!1;switch(e.key){case"ArrowUp":if(!(this.virtualSelectedIndex>0))return;t=!0,this.virtualSelectedIndex--;break;case"ArrowDown":if(!(this.virtualSelectedIndex<this.itemCount-1))return;this.virtualSelectedIndex++;break;case"Home":this.virtualSelectedIndex=0;break;case"End":this.virtualSelectedIndex=this.itemCount-1;break;default:return}e.consume(!0),this.scrollItemIntoView(this.virtualSelectedIndex),this.updateFocusedItem(t)}updateFocusedItem(e){const t=this.renderedElementAt(this.virtualSelectedIndex),n=this.lastSelectedElement!==t,i=this.contentElementInternal===s.DOMUtilities.deepActiveElement(this.element.ownerDocument);if(this.lastSelectedElement&&n&&this.lastSelectedElement.classList.remove("console-selected"),t&&(e||n||i)&&this.element.hasFocus()){t.classList.add("console-selected");const s=pe(t);s&&o.Context.Context.instance().setFlavor(ve,s),e?(this.setStickToBottom(!1),this.renderedItems[this.virtualSelectedIndex-this.firstActiveIndex].focusLastChildOrSelf()):t.hasFocus()||t.focus({preventScroll:!0})}this.itemCount&&!this.contentElementInternal.hasFocus()?this.contentElementInternal.tabIndex=0:this.contentElementInternal.tabIndex=-1,this.lastSelectedElement=t}contentElement(){return this.contentElementInternal}invalidate(){delete this.cachedProviderElements,this.itemCount=this.provider.itemCount(),this.virtualSelectedIndex>this.itemCount-1&&(this.virtualSelectedIndex=this.itemCount-1),this.rebuildCumulativeHeights(),this.refresh()}providerElement(e){this.cachedProviderElements||(this.cachedProviderElements=new Array(this.itemCount));let t=this.cachedProviderElements[e];return t||(t=this.provider.itemElement(e),this.cachedProviderElements[e]=t),t}rebuildCumulativeHeights(){const e=this.firstActiveIndex,t=this.lastActiveIndex;let s=0;this.cumulativeHeights=new Int32Array(this.itemCount);for(let n=0;n<this.itemCount;++n)e<=n&&n-e<this.renderedItems.length&&n<=t?s+=this.renderedItems[n-e].element().offsetHeight:s+=this.provider.fastHeight(n),this.cumulativeHeights[n]=s}rebuildCumulativeHeightsIfNeeded(){let e=0,t=0;for(let s=0;s<this.renderedItems.length;++s){const n=this.cachedItemHeight(this.firstActiveIndex+s),o=this.renderedItems[s].element().offsetHeight;if(Math.abs(n-o)>1)return void this.rebuildCumulativeHeights();if(t+=o,e+=n,Math.abs(e-t)>1)return void this.rebuildCumulativeHeights()}}cachedItemHeight(e){return 0===e?this.cumulativeHeights[0]:this.cumulativeHeights[e]-this.cumulativeHeights[e-1]}isSelectionBackwards(e){if(!(e&&e.rangeCount&&e.anchorNode&&e.focusNode))return!1;const t=document.createRange();return t.setStart(e.anchorNode,e.anchorOffset),t.setEnd(e.focusNode,e.focusOffset),t.collapsed}createSelectionModel(e,t,s){return{item:e,node:t,offset:s}}updateSelectionModel(e){const t=e&&e.rangeCount?e.getRangeAt(0):null;if(!t||!e||e.isCollapsed||!this.element.hasSelection())return this.headSelection=null,this.anchorSelection=null,!1;let s=Number.MAX_VALUE,n=-1,o=!1;for(let e=0;e<this.renderedItems.length;++e)if(t.intersectsNode(this.renderedItems[e].element())){const t=e+this.firstActiveIndex;s=Math.min(s,t),n=Math.max(n,t),o=!0}const i=t.intersectsNode(this.topGapElement)&&this.topGapElementActive,r=t.intersectsNode(this.bottomGapElement)&&this.bottomGapElementActive;if(!i&&!r&&!o)return this.headSelection=null,this.anchorSelection=null,!1;this.anchorSelection&&this.headSelection||(this.anchorSelection=this.createSelectionModel(0,this.element,0),this.headSelection=this.createSelectionModel(this.itemCount-1,this.element,this.element.children.length),this.selectionIsBackward=!1);const a=this.isSelectionBackwards(e),l=this.selectionIsBackward?this.headSelection:this.anchorSelection,c=this.selectionIsBackward?this.anchorSelection:this.headSelection;let h=null,d=null;return o&&(h=this.createSelectionModel(s,t.startContainer,t.startOffset),d=this.createSelectionModel(n,t.endContainer,t.endOffset)),i&&r&&o?(h=h&&h.item<l.item?h:l,d=d&&d.item>c.item?d:c):o?i?h=a?this.headSelection:this.anchorSelection:r&&(d=a?this.anchorSelection:this.headSelection):(h=l,d=c),a?(this.anchorSelection=d,this.headSelection=h):(this.anchorSelection=h,this.headSelection=d),this.selectionIsBackward=a,!0}restoreSelection(e){if(!e||!this.anchorSelection||!this.headSelection)return;const t=(e,t)=>{if(this.firstActiveIndex<=e.item&&e.item<=this.lastActiveIndex)return{element:e.node,offset:e.offset};return{element:e.item<this.firstActiveIndex?this.topGapElement:this.bottomGapElement,offset:t?1:0}},{element:s,offset:n}=t(this.anchorSelection,Boolean(this.selectionIsBackward)),{element:o,offset:i}=t(this.headSelection,!this.selectionIsBackward);e.setBaseAndExtent(s,n,o,i)}selectionContainsTable(){if(!this.anchorSelection||!this.headSelection)return!1;const e=this.selectionIsBackward?this.headSelection.item:this.anchorSelection.item,t=this.selectionIsBackward?this.anchorSelection.item:this.headSelection.item;for(let s=e;s<=t;s++){const e=this.providerElement(s);if(e&&"table"===e.consoleMessage().type)return!0}return!1}refresh(){this.observer.disconnect(),this.innerRefresh(),this.stickToBottomInternal&&this.observer.observe(this.contentElementInternal,this.observerConfig)}innerRefresh(){if(!this.visibleHeight())return;if(!this.itemCount){for(let e=0;e<this.renderedItems.length;++e)this.renderedItems[e].willHide();return this.renderedItems=[],this.contentElementInternal.removeChildren(),this.topGapElement.style.height="0px",this.bottomGapElement.style.height="0px",this.firstActiveIndex=-1,this.lastActiveIndex=-1,void this.updateFocusedItem()}const e=this.element.getComponentSelection(),t=this.updateSelectionModel(e),n=this.element.scrollTop,o=this.visibleHeight(),i=2*o;this.rebuildCumulativeHeightsIfNeeded(),this.stickToBottomInternal?(this.firstActiveIndex=Math.max(this.itemCount-Math.ceil(i/this.provider.minimumRowHeight()),0),this.lastActiveIndex=this.itemCount-1):(this.firstActiveIndex=Math.max(s.ArrayUtilities.lowerBound(this.cumulativeHeights,n+1-(i-o)/2,s.ArrayUtilities.DEFAULT_COMPARATOR),0),this.lastActiveIndex=this.firstActiveIndex+Math.ceil(i/this.provider.minimumRowHeight())-1,this.lastActiveIndex=Math.min(this.lastActiveIndex,this.itemCount-1));const r=this.cumulativeHeights[this.firstActiveIndex-1]||0,a=this.cumulativeHeights[this.cumulativeHeights.length-1]-this.cumulativeHeights[this.lastActiveIndex];this.partialViewportUpdate(function(){this.topGapElement.style.height=r+"px",this.bottomGapElement.style.height=a+"px",this.topGapElementActive=Boolean(r),this.bottomGapElementActive=Boolean(a),this.contentElementInternal.style.setProperty("height","10000000px")}.bind(this)),this.contentElementInternal.style.removeProperty("height"),t&&this.restoreSelection(e),this.stickToBottomInternal&&(this.element.scrollTop=1e7)}partialViewportUpdate(e){const t=new Set;for(let e=this.firstActiveIndex;e<=this.lastActiveIndex;++e){const s=this.providerElement(e);console.assert(Boolean(s),"Expected provider element to be defined"),s&&t.add(s)}const s=this.renderedItems.filter((e=>!t.has(e)));for(let e=0;e<s.length;++e)s[e].willHide();e();let n=!1;for(let e=0;e<s.length;++e)n=n||s[e].element().hasFocus(),s[e].element().remove();const o=[];let i=this.contentElementInternal.firstChild;for(const e of t){const t=e.element();if(t!==i){!t.parentElement&&o.push(e),this.contentElementInternal.insertBefore(t,i)}else i=i.nextSibling}for(let e=0;e<o.length;++e)o[e].wasShown();this.renderedItems=Array.from(t),n&&this.contentElementInternal.focus(),this.updateFocusedItem()}selectedText(){if(this.updateSelectionModel(this.element.getComponentSelection()),!this.headSelection||!this.anchorSelection)return null;let e=null,t=null;this.selectionIsBackward?(e=this.headSelection,t=this.anchorSelection):(e=this.anchorSelection,t=this.headSelection);const s=[];for(let n=e.item;n<=t.item;++n){const e=this.providerElement(n);if(console.assert(Boolean(e)),!e)continue;const t=e.element().childTextNodes().map(u.Linkifier.Linkifier.untruncatedNodeText).join("");s.push(t)}const n=this.providerElement(t.item),o=n&&n.element();if(o&&t.node&&t.node.isSelfOrDescendant(o)){const e=this.textOffsetInNode(o,t.node,t.offset);s.length>0&&(s[s.length-1]=s[s.length-1].substring(0,e))}const i=this.providerElement(e.item),r=i&&i.element();if(r&&e.node&&e.node.isSelfOrDescendant(r)){const t=this.textOffsetInNode(r,e.node,e.offset);s[0]=s[0].substring(t)}return s.join("\n")}textOffsetInNode(e,t,s){const n=t.textContent?t.textContent.length:0;t.nodeType!==Node.TEXT_NODE&&(s<t.childNodes.length?(t=t.childNodes.item(s),s=0):s=n);let o=0,i=e;for(;(i=i.traverseNextNode(e))&&i!==t;)i.nodeType!==Node.TEXT_NODE||i.parentNode&&("STYLE"===i.parentNode.nodeName||"SCRIPT"===i.parentNode.nodeName||"#document-fragment"===i.parentNode.nodeName)||(o+=u.Linkifier.Linkifier.untruncatedNodeText(i).length);const r=u.Linkifier.Linkifier.untruncatedNodeText(t).length;return s>0&&r!==n&&(s=r),o+s}onScroll(e){this.refresh()}firstVisibleIndex(){return this.cumulativeHeights.length?(this.rebuildCumulativeHeightsIfNeeded(),s.ArrayUtilities.lowerBound(this.cumulativeHeights,this.element.scrollTop+1,s.ArrayUtilities.DEFAULT_COMPARATOR)):-1}lastVisibleIndex(){if(!this.cumulativeHeights.length)return-1;this.rebuildCumulativeHeightsIfNeeded();const e=this.element.scrollTop+this.element.clientHeight,t=this.itemCount-1;return s.ArrayUtilities.lowerBound(this.cumulativeHeights,e,s.ArrayUtilities.DEFAULT_COMPARATOR,void 0,t)}renderedElementAt(e){return-1===e||e<this.firstActiveIndex||e>this.lastActiveIndex?null:this.renderedItems[e-this.firstActiveIndex].element()}scrollItemIntoView(e,t){const s=this.firstVisibleIndex(),n=this.lastVisibleIndex();e>s&&e<n||e===n&&this.cumulativeHeights[e]<=this.element.scrollTop+this.visibleHeight()||(t?this.forceScrollItemToBeLast(e):e<=s?this.forceScrollItemToBeFirst(e):e>=n&&this.forceScrollItemToBeLast(e))}forceScrollItemToBeFirst(e){console.assert(e>=0&&e<this.itemCount,"Cannot scroll item at invalid index"),this.setStickToBottom(!1),this.rebuildCumulativeHeightsIfNeeded(),this.element.scrollTop=e>0?this.cumulativeHeights[e-1]:0,o.UIUtils.isScrolledToBottom(this.element)&&this.setStickToBottom(!0),this.refresh();const t=this.renderedElementAt(e);t&&t.scrollIntoView(!0)}forceScrollItemToBeLast(e){console.assert(e>=0&&e<this.itemCount,"Cannot scroll item at invalid index"),this.setStickToBottom(!1),this.rebuildCumulativeHeightsIfNeeded(),this.element.scrollTop=this.cumulativeHeights[e]-this.visibleHeight(),o.UIUtils.isScrolledToBottom(this.element)&&this.setStickToBottom(!0),this.refresh();const t=this.renderedElementAt(e);t&&t.scrollIntoView(!1)}visibleHeight(){return this.element.offsetHeight}}var Ae=Object.freeze({__proto__:null,ConsoleViewport:Re});const Pe={issuesWithColon:"{n, plural, =0 {No Issues} =1 {# Issue:} other {# Issues:}}",issueToolbarTooltipGeneral:"Some problems no longer generate console messages, but are surfaced in the issues tab.",issueToolbarClickToView:"Click to view {issueEnumeration}",issueToolbarClickToGoToTheIssuesTab:"Click to go to the issues tab",findStringInLogs:"Find string in logs",consoleSettings:"Console settings",groupSimilarMessagesInConsole:"Group similar messages in console",showCorsErrorsInConsole:"Show `CORS` errors in console",showConsoleSidebar:"Show console sidebar",hideConsoleSidebar:"Hide console sidebar",consoleSidebarShown:"Console sidebar shown",consoleSidebarHidden:"Console sidebar hidden",doNotClearLogOnPageReload:"Do not clear log on page reload / navigation",preserveLog:"Preserve log",hideNetwork:"Hide network",onlyShowMessagesFromTheCurrentContext:"Only show messages from the current context (`top`, `iframe`, `worker`, extension)",selectedContextOnly:"Selected context only",logXMLHttpRequests:"Log XMLHttpRequests",eagerlyEvaluateTextInThePrompt:"Eagerly evaluate text in the prompt",autocompleteFromHistory:"Autocomplete from history",treatEvaluationAsUserActivation:"Treat evaluation as user activation",sHidden:"{n, plural, =1 {# hidden} other {# hidden}}",consoleCleared:"Console cleared",hideMessagesFromS:"Hide messages from {PH1}",saveAs:"Save as...",copyVisibleStyledSelection:"Copy visible styled selection",replayXhr:"Replay XHR",writingFile:"Writing file…",searching:"Searching…",egEventdCdnUrlacom:"e.g. `/eventd/ -cdn url:a.com`",verbose:"Verbose",info:"Info",warnings:"Warnings",errors:"Errors",overriddenByFilterSidebar:"Overridden by filter sidebar",customLevels:"Custom levels",sOnly:"{PH1} only",allLevels:"All levels",defaultLevels:"Default levels",hideAll:"Hide all",logLevelS:"Log level: {PH1}",default:"Default",filteredMessagesInConsole:"{PH1} messages in console"},Ue=t.i18n.registerUIStrings("panels/console/ConsoleView.ts",Pe),Be=t.i18n.getLocalizedString.bind(void 0,Ue);let He;class je extends o.Widget.VBox{searchableViewInternal;sidebar;isSidebarOpen;filter;consoleToolbarContainer;splitWidget;contentsElement;visibleViewMessages;hiddenByFilterCount;shouldBeHiddenCache;lastShownHiddenByFilterCount;currentMatchRangeIndex;searchRegex;groupableMessages;groupableMessageTitle;shortcuts;regexMatchRanges;consoleContextSelector;filterStatusText;showSettingsPaneSetting;showSettingsPaneButton;progressToolbarItem;groupSimilarSetting;showCorsErrorsSetting;timestampsSetting;consoleHistoryAutocompleteSetting;selfXssWarningDisabledSetting;viewport;messagesElement;messagesCountElement;viewportThrottler;pendingBatchResize;onMessageResizedBound;promptElement;linkifier;consoleMessages;consoleGroupStarts;prompt;immediatelyFilterMessagesForTest;maybeDirtyWhileMuted;scheduledRefreshPromiseForTest;needsFullUpdate;buildHiddenCacheTimeout;searchShouldJumpBackwards;searchProgressIndicator;innerSearchTimeoutId;muteViewportUpdates;waitForScrollTimeout;issueCounter;pendingSidebarMessages=[];userHasOpenedSidebarAtLeastOnce=!1;issueToolbarThrottle;requestResolver=new g.RequestResolver.RequestResolver;issueResolver=new I.IssueResolver.IssueResolver;#o=!1;#i=this.#r.bind(this);constructor(t){super(),this.setMinimumSize(0,35),this.searchableViewInternal=new o.SearchableView.SearchableView(this,null),this.searchableViewInternal.element.classList.add("console-searchable-view"),this.searchableViewInternal.setPlaceholder(Be(Pe.findStringInLogs)),this.searchableViewInternal.setMinimalSearchQuerySize(0),this.sidebar=new Z,this.sidebar.addEventListener("FilterSelected",this.onFilterChanged.bind(this)),this.isSidebarOpen=!1,this.filter=new Oe(this.onFilterChanged.bind(this)),this.consoleToolbarContainer=this.element.createChild("div","console-toolbar-container"),this.splitWidget=new o.SplitWidget.SplitWidget(!0,!1,"console.sidebar.width",100),this.splitWidget.setMainWidget(this.searchableViewInternal),this.splitWidget.setSidebarWidget(this.sidebar),this.splitWidget.show(this.element),this.splitWidget.hideSidebar(),this.splitWidget.enableShowModeSaving(),this.isSidebarOpen="Both"===this.splitWidget.showMode(),this.filter.setLevelMenuOverridden(this.isSidebarOpen),this.splitWidget.addEventListener("ShowModeChanged",(e=>{this.isSidebarOpen="Both"===e.data,this.isSidebarOpen&&(this.userHasOpenedSidebarAtLeastOnce||(p.userMetrics.actionTaken(p.UserMetrics.Action.ConsoleSidebarOpened),this.userHasOpenedSidebarAtLeastOnce=!0),this.pendingSidebarMessages.forEach((e=>{this.sidebar.onMessageAdded(e)})),this.pendingSidebarMessages=[]),this.filter.setLevelMenuOverridden(this.isSidebarOpen),this.onFilterChanged()})),this.contentsElement=this.searchableViewInternal.element,this.element.classList.add("console-view"),this.visibleViewMessages=[],this.hiddenByFilterCount=0,this.shouldBeHiddenCache=new Set,this.groupableMessages=new Map,this.groupableMessageTitle=new Map,this.shortcuts=new Map,this.regexMatchRanges=[],this.consoleContextSelector=new k,this.filterStatusText=new o.Toolbar.ToolbarText,this.filterStatusText.element.classList.add("dimmed"),this.showSettingsPaneSetting=e.Settings.Settings.instance().createSetting("console-show-settings-toolbar",!1),this.showSettingsPaneButton=new o.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"gear",Be(Pe.consoleSettings),"gear-filled"),this.showSettingsPaneButton.element.setAttribute("jslog",`${h.toggleSubpane("console-settings").track({click:!0})}`),this.progressToolbarItem=new o.Toolbar.ToolbarItem(document.createElement("div")),this.groupSimilarSetting=e.Settings.Settings.instance().moduleSetting("console-group-similar"),this.groupSimilarSetting.addChangeListener((()=>this.updateMessageList())),this.showCorsErrorsSetting=e.Settings.Settings.instance().moduleSetting("console-shows-cors-errors"),this.showCorsErrorsSetting.addChangeListener((()=>this.updateMessageList()));const s=new o.Toolbar.Toolbar("console-main-toolbar",this.consoleToolbarContainer);s.makeWrappable(!0),s.appendToolbarItem(this.splitWidget.createShowHideSidebarButton(Be(Pe.showConsoleSidebar),Be(Pe.hideConsoleSidebar),Be(Pe.consoleSidebarShown),Be(Pe.consoleSidebarHidden),"console-sidebar")),s.appendToolbarItem(o.Toolbar.Toolbar.createActionButtonForId("console.clear")),s.appendSeparator(),s.appendToolbarItem(this.consoleContextSelector.toolbarItem()),s.appendSeparator(),s.appendSeparator(),s.appendToolbarItem(this.filter.textFilterUI),s.appendToolbarItem(this.filter.levelMenuButton),s.appendToolbarItem(this.progressToolbarItem),s.appendSeparator(),s.element.setAttribute("jslog",`${h.toolbar()}`),this.issueCounter=new b.IssueCounter.IssueCounter,this.issueCounter.id="console-issues-counter",this.issueCounter.setAttribute("jslog",`${h.counter("issues").track({click:!0})}`);const i=new o.Toolbar.ToolbarItem(this.issueCounter);this.issueCounter.data={clickHandler:()=>{p.userMetrics.issuesPanelOpenedFrom(2),o.ViewManager.ViewManager.instance().showView("issues-pane")},issuesManager:I.IssuesManager.IssuesManager.instance(),accessibleName:Be(Pe.issueToolbarTooltipGeneral),displayMode:"OmitEmpty"},s.appendToolbarItem(i),s.appendSeparator(),s.appendToolbarItem(this.filterStatusText),s.appendToolbarItem(this.showSettingsPaneButton);const r=e.Settings.Settings.instance().moduleSetting("monitoring-xhr-enabled");this.timestampsSetting=e.Settings.Settings.instance().moduleSetting("console-timestamps-enabled"),this.consoleHistoryAutocompleteSetting=e.Settings.Settings.instance().moduleSetting("console-history-autocomplete"),this.selfXssWarningDisabledSetting=e.Settings.Settings.instance().createSetting("disable-self-xss-warning",!1,"Synced");const a=new o.Widget.HBox;a.show(this.contentsElement),a.element.classList.add("console-settings-pane"),o.ARIAUtils.setLabel(a.element,Be(Pe.consoleSettings)),o.ARIAUtils.markAsGroup(a.element);const l=new o.Toolbar.Toolbar("",a.element);l.makeVertical(),je.appendSettingsCheckboxToToolbar(l,this.filter.hideNetworkMessagesSetting,this.filter.hideNetworkMessagesSetting.title(),Be(Pe.hideNetwork)),je.appendSettingsCheckboxToToolbar(l,"preserve-console-log",Be(Pe.doNotClearLogOnPageReload),Be(Pe.preserveLog)),je.appendSettingsCheckboxToToolbar(l,this.filter.filterByExecutionContextSetting,Be(Pe.onlyShowMessagesFromTheCurrentContext),Be(Pe.selectedContextOnly)),je.appendSettingsCheckboxToToolbar(l,this.groupSimilarSetting,Be(Pe.groupSimilarMessagesInConsole)),je.appendSettingsCheckboxToToolbar(l,this.showCorsErrorsSetting,Be(Pe.showCorsErrorsInConsole));const c=new o.Toolbar.Toolbar("",a.element);c.makeVertical(),je.appendSettingsCheckboxToToolbar(c,r,Be(Pe.logXMLHttpRequests)),je.appendSettingsCheckboxToToolbar(c,this.consoleHistoryAutocompleteSetting,Be(Pe.autocompleteFromHistory)),je.appendSettingsCheckboxToToolbar(c,"console-user-activation-eval",Be(Pe.treatEvaluationAsUserActivation)),this.showSettingsPaneSetting.get()||a.element.classList.add("hidden"),this.showSettingsPaneSetting.addChangeListener((()=>a.element.classList.toggle("hidden",!this.showSettingsPaneSetting.get()))),this.viewport=new Re(this),this.viewport.setStickToBottom(!0),this.viewport.contentElement().classList.add("console-group","console-group-messages"),this.contentsElement.appendChild(this.viewport.element),this.messagesElement=this.viewport.element,this.messagesElement.id="console-messages",this.messagesElement.classList.add("monospace"),this.messagesElement.addEventListener("click",this.messagesClicked.bind(this),!1),["paste","clipboard-paste","drop"].forEach((e=>{this.messagesElement.addEventListener(e,this.messagesPasted.bind(this),!0)})),this.messagesCountElement=this.consoleToolbarContainer.createChild("div","message-count"),o.ARIAUtils.markAsPoliteLiveRegion(this.messagesCountElement,!1),this.viewportThrottler=new e.Throttler.Throttler(t),this.pendingBatchResize=!1,this.onMessageResizedBound=e=>{this.onMessageResized(e)},this.promptElement=this.messagesElement.createChild("div","source-code"),this.promptElement.id="console-prompt";const d=this.messagesElement.createChild("div","console-view-fix-select-all");d.textContent=".",o.ARIAUtils.markAsHidden(d),this.registerShortcuts(),this.messagesElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1);const m=new e.Throttler.Throttler(100);this.linkifier=new u.Linkifier.Linkifier(40),this.linkifier.addEventListener("liveLocationUpdated",(()=>m.schedule((async()=>this.onFilterChanged())))),this.consoleMessages=[],this.consoleGroupStarts=[],this.prompt=new Qe,this.prompt.show(this.promptElement),this.prompt.element.addEventListener("keydown",this.promptKeyDown.bind(this),!0),this.prompt.addEventListener("TextChanged",this.promptTextChanged,this),this.messagesElement.addEventListener("keydown",this.messagesKeyDown.bind(this),!1),this.prompt.element.addEventListener("focusin",(()=>{this.isScrolledToBottom()&&this.viewport.setStickToBottom(!0)})),this.consoleHistoryAutocompleteSetting.addChangeListener(this.consoleHistoryAutocompleteChanged,this),this.consoleHistoryAutocompleteChanged(),this.updateFilterStatus(),this.timestampsSetting.addChangeListener(this.consoleTimestampsSettingChanged,this),this.registerWithMessageSink(),o.Context.Context.instance().addFlavorChangeListener(n.RuntimeModel.ExecutionContext,this.executionContextChanged,this),this.messagesElement.addEventListener("mousedown",(e=>this.updateStickToBottomOnPointerDown(2===e.button)),!1),this.messagesElement.addEventListener("mouseup",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("mouseleave",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("wheel",this.updateStickToBottomOnWheel.bind(this),!1),this.messagesElement.addEventListener("touchstart",this.updateStickToBottomOnPointerDown.bind(this,!1),!1),this.messagesElement.addEventListener("touchend",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("touchcancel",this.updateStickToBottomOnPointerUp.bind(this),!1),n.TargetManager.TargetManager.instance().addModelListener(n.ConsoleModel.ConsoleModel,n.ConsoleModel.Events.ConsoleCleared,this.consoleCleared,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ConsoleModel.ConsoleModel,n.ConsoleModel.Events.MessageAdded,this.onConsoleMessageAdded,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ConsoleModel.ConsoleModel,n.ConsoleModel.Events.MessageUpdated,this.onConsoleMessageUpdated,this,{scoped:!0}),n.TargetManager.TargetManager.instance().addModelListener(n.ConsoleModel.ConsoleModel,n.ConsoleModel.Events.CommandEvaluated,this.commandEvaluated,this,{scoped:!0}),n.TargetManager.TargetManager.instance().observeModels(n.ConsoleModel.ConsoleModel,this,{scoped:!0});const g=I.IssuesManager.IssuesManager.instance();this.issueToolbarThrottle=new e.Throttler.Throttler(100),g.addEventListener("IssuesCountUpdated",this.#i)}static appendSettingsCheckboxToToolbar(t,s,n,i){let r;r="string"==typeof s?e.Settings.Settings.instance().moduleSetting(s):s;const a=new o.Toolbar.ToolbarSettingCheckbox(r,n,i);return t.appendToolbarItem(a),a}static instance(e){return He&&!e?.forceNew||(He=new je(e?.viewportThrottlerTimeout??50)),He}static clearConsole(){n.ConsoleModel.ConsoleModel.requestClearMessages()}#r(){this.issueToolbarThrottle.schedule((async()=>this.updateIssuesToolbarItem())),this.issuesCountUpdatedForTest()}issuesCountUpdatedForTest(){}modelAdded(e){e.messages().forEach(this.addConsoleMessage,this)}modelRemoved(t){e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()||t.target().outermostTarget()!==t.target()||this.consoleCleared()}onFilterChanged(){if(this.filter.currentFilter.levelsMask=this.isSidebarOpen?R.allLevelsFilterValue():this.filter.messageLevelFiltersSetting.get(),this.cancelBuildHiddenCache(),this.immediatelyFilterMessagesForTest){for(const e of this.consoleMessages)this.computeShouldMessageBeVisible(e);this.updateMessageList()}else this.buildHiddenCache(0,this.consoleMessages.slice())}setImmediatelyFilterMessagesForTest(){this.immediatelyFilterMessagesForTest=!0}searchableView(){return this.searchableViewInternal}clearHistory(){this.prompt.history().clear()}consoleHistoryAutocompleteChanged(){this.prompt.setAddCompletionsFromHistory(this.consoleHistoryAutocompleteSetting.get())}itemCount(){return this.visibleViewMessages.length}itemElement(e){return this.visibleViewMessages[e]}fastHeight(e){return this.visibleViewMessages[e].fastHeight()}minimumRowHeight(){return 16}registerWithMessageSink(){e.Console.Console.instance().messages().forEach(this.addSinkMessage,this),e.Console.Console.instance().addEventListener("messageAdded",(({data:e})=>{this.addSinkMessage(e)}),this)}addSinkMessage(e){let t="verbose";switch(e.level){case"info":t="info";break;case"error":t="error";break;case"warning":t="warning"}const s=e.source||"other",o=new n.ConsoleModel.ConsoleMessage(null,s,t,e.text,{type:n.ConsoleModel.FrontendMessageType.System,timestamp:e.timestamp});this.addConsoleMessage(o)}consoleTimestampsSettingChanged(){this.updateMessageList(),this.consoleMessages.forEach((e=>e.updateTimestamp())),this.groupableMessageTitle.forEach((e=>e.updateTimestamp()))}executionContextChanged(){this.prompt.clearAutocomplete()}willHide(){this.hidePromptSuggestBox()}wasShown(){if(super.wasShown(),this.#o){I.IssuesManager.IssuesManager.instance().addEventListener("IssuesCountUpdated",this.#i)}this.#o=!1,this.updateIssuesToolbarItem(),this.viewport.refresh(),this.registerCSSFiles([ne,O,v.Style.default])}focus(){this.viewport.hasVirtualSelection()?this.viewport.contentElement().focus():this.focusPrompt()}focusPrompt(){if(!this.prompt.hasFocus()){const e=this.viewport.stickToBottom(),t=this.viewport.element.scrollTop;this.prompt.focus(),this.viewport.setStickToBottom(e),this.viewport.element.scrollTop=t}}restoreScrollPositions(){this.viewport.stickToBottom()?this.immediatelyScrollToBottom():super.restoreScrollPositions()}onResize(){this.scheduleViewportRefresh(),this.hidePromptSuggestBox(),this.viewport.stickToBottom()&&this.immediatelyScrollToBottom();for(let e=0;e<this.visibleViewMessages.length;++e)this.visibleViewMessages[e].onResize()}hidePromptSuggestBox(){this.prompt.clearAutocomplete()}async invalidateViewport(){this.updateIssuesToolbarItem(),this.muteViewportUpdates?this.maybeDirtyWhileMuted=!0:this.needsFullUpdate?(this.updateMessageList(),delete this.needsFullUpdate):this.viewport.invalidate()}onDetach(){this.#o=!0;I.IssuesManager.IssuesManager.instance().removeEventListener("IssuesCountUpdated",this.#i)}updateIssuesToolbarItem(){if(this.#o)return;const e=I.IssuesManager.IssuesManager.instance(),t=b.IssueCounter.getIssueCountsEnumeration(e),s=0===e.numberOfIssues()?Be(Pe.issueToolbarClickToGoToTheIssuesTab):Be(Pe.issueToolbarClickToView,{issueEnumeration:t}),n=`${Be(Pe.issueToolbarTooltipGeneral)} ${s}`;o.Tooltip.Tooltip.install(this.issueCounter,n),this.issueCounter.data={...this.issueCounter.data,leadingText:Be(Pe.issuesWithColon,{n:e.numberOfIssues()}),accessibleName:n}}scheduleViewportRefresh(){this.muteViewportUpdates?this.maybeDirtyWhileMuted=!0:this.scheduledRefreshPromiseForTest=this.viewportThrottler.schedule(this.invalidateViewport.bind(this))}getScheduledRefreshPromiseForTest(){return this.scheduledRefreshPromiseForTest}immediatelyScrollToBottom(){this.viewport.setStickToBottom(!0),this.promptElement.scrollIntoView(!0)}updateFilterStatus(){this.hiddenByFilterCount!==this.lastShownHiddenByFilterCount&&(this.filterStatusText.setText(Be(Pe.sHidden,{n:this.hiddenByFilterCount})),this.filterStatusText.setVisible(Boolean(this.hiddenByFilterCount)),this.lastShownHiddenByFilterCount=this.hiddenByFilterCount)}onConsoleMessageAdded(e){const t=e.data;this.addConsoleMessage(t)}addConsoleMessage(e){const t=this.createViewMessage(e);if(Ne.set(e,t),e.type===n.ConsoleModel.FrontendMessageType.Command||e.type===n.ConsoleModel.FrontendMessageType.Result){const e=this.consoleMessages[this.consoleMessages.length-1],s=e&&Ve.get(e)||0;Ve.set(t,s)}else Ve.set(t,t.consoleMessage().timestamp);let o;o=!this.consoleMessages.length||l(t,this.consoleMessages[this.consoleMessages.length-1])>0?this.consoleMessages.length:s.ArrayUtilities.upperBound(this.consoleMessages,t,l);const i=o<this.consoleMessages.length;if(this.consoleMessages.splice(o,0,t),e.type===n.ConsoleModel.FrontendMessageType.Command)this.prompt.history().pushHistoryItem(e.messageText),this.prompt.history().length()>=5&&!this.selfXssWarningDisabledSetting.get()&&this.selfXssWarningDisabledSetting.set(!0);else if(e.type!==n.ConsoleModel.FrontendMessageType.Result){const n=s.ArrayUtilities.upperBound(this.consoleGroupStarts,t,l)-1;if(n>=0){!function e(t,s){const n=s.groupEnd();if(null!==n&&l(t,n)>0){const n=s.consoleGroup();if(null===n)return;return void e(t,n)}"endGroup"===t.consoleMessage().type?s.setGroupEnd(t):t.setConsoleGroup(s)}(t,this.consoleGroupStarts[n])}e.isGroupStartMessage()&&(o=s.ArrayUtilities.upperBound(this.consoleGroupStarts,t,l),this.consoleGroupStarts.splice(o,0,t))}this.filter.onMessageAdded(e),this.isSidebarOpen?this.sidebar.onMessageAdded(t):this.pendingSidebarMessages.push(t);let r=!1;const a=this.groupSimilarSetting.get();if(e.isGroupable()){const e=t.groupKey();r=a&&this.groupableMessages.has(e);let s=this.groupableMessages.get(e);s||(s=[],this.groupableMessages.set(e,s)),s.push(t)}function l(e,t){return(Ve.get(e)||0)-(Ve.get(t)||0)}this.computeShouldMessageBeVisible(t),r||i?this.needsFullUpdate=!0:(this.appendMessageToEnd(t,!a),this.updateFilterStatus(),this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length)),this.scheduleViewportRefresh(),this.consoleMessageAddedForTest(t)}onConsoleMessageUpdated(e){const t=e.data,s=Ne.get(t);s&&(s.updateMessageElement(),this.computeShouldMessageBeVisible(s),this.updateMessageList())}consoleMessageAddedForTest(e){}shouldMessageBeVisible(e){return!this.shouldBeHiddenCache.has(e)}computeShouldMessageBeVisible(e){!this.filter.shouldBeVisible(e)||this.isSidebarOpen&&!this.sidebar.shouldBeVisible(e)?this.shouldBeHiddenCache.add(e):this.shouldBeHiddenCache.delete(e)}appendMessageToEnd(e,t){if("cors"===e.consoleMessage().category&&!this.showCorsErrorsSetting.get())return;const s=this.visibleViewMessages[this.visibleViewMessages.length-1];if("endGroup"===e.consoleMessage().type){if(s){const e=s.consoleGroup();e&&!e.messagesHidden()&&s.incrementCloseGroupDecorationCount()}return}if(!this.shouldMessageBeVisible(e))return void this.hiddenByFilterCount++;if(!t&&this.tryToCollapseMessages(e,this.visibleViewMessages[this.visibleViewMessages.length-1]))return;const n=e.consoleGroup();if(!n||!n.messagesHidden()){const t=e.consoleMessage().originatingMessage(),o=Boolean(t&&s?.consoleMessage()===t);e.setAdjacentUserCommandResult(o),function e(t,s){if(null===t)return;if(s.includes(t))return;const n=t.consoleGroup();n&&e(n,s);s.push(t)}(n,this.visibleViewMessages),this.visibleViewMessages.push(e),this.searchMessage(this.visibleViewMessages.length-1)}this.messageAppendedForTests()}messageAppendedForTests(){}createViewMessage(e){switch(e.type){case n.ConsoleModel.FrontendMessageType.Command:return new Se(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);case n.ConsoleModel.FrontendMessageType.Result:return new Ie(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);case"startGroupCollapsed":case"startGroup":return new we(e,this.linkifier,this.requestResolver,this.issueResolver,this.updateMessageList.bind(this),this.onMessageResizedBound);case"table":return new ye(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);default:return new ve(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound)}}async onMessageResized(e){const t=e.data;if(this.pendingBatchResize||!t.treeOutline)return;this.pendingBatchResize=!0,await Promise.resolve();const s=t.treeOutline.element;this.viewport.setStickToBottom(this.isScrolledToBottom()),s.offsetHeight<=this.messagesElement.offsetHeight&&s.scrollIntoViewIfNeeded(),this.pendingBatchResize=!1}consoleCleared(){const e=this.viewport.element.hasFocus();this.cancelBuildHiddenCache(),this.currentMatchRangeIndex=-1,this.consoleMessages=[],this.groupableMessages.clear(),this.groupableMessageTitle.clear(),this.sidebar.clear(),this.pendingSidebarMessages=[],this.updateMessageList(),this.hidePromptSuggestBox(),this.viewport.setStickToBottom(!0),this.linkifier.reset(),this.filter.clear(),this.requestResolver.clear(),this.consoleGroupStarts=[],e&&this.prompt.focus(),o.ARIAUtils.alert(Be(Pe.consoleCleared))}handleContextMenuEvent(t){const s=new o.ContextMenu.ContextMenu(t),i=t.target;if(i.isSelfOrDescendant(this.promptElement))return void s.show();const r=i.enclosingNodeOrSelfWithClass("console-message-wrapper"),a=r&&pe(r),l=a?a.consoleMessage():null;if(a&&o.Context.Context.instance().setFlavor(ve,a),l&&!a?.element()?.matches(".has-insight")&&a?.shouldShowInsights()&&s.headerSection().appendAction(a?.getExplainActionId(),void 0,!0),l&&l.url){const t=Be(Pe.hideMessagesFromS,{PH1:new e.ParsedURL.ParsedURL(l.url).displayName});s.headerSection().appendItem(t,this.filter.addMessageURLFilter.bind(this.filter,l.url),{jslogContext:"hide-messages-from"})}if(s.defaultSection().appendAction("console.clear"),s.defaultSection().appendAction("console.clear.history"),s.saveSection().appendItem(Be(Pe.saveAs),this.saveConsole.bind(this),{jslogContext:"save-as"}),this.element.hasSelection()&&s.clipboardSection().appendItem(Be(Pe.copyVisibleStyledSelection),this.viewport.copyWithStyles.bind(this.viewport),{jslogContext:"copy-visible-styled-selection"}),l){const e=g.NetworkLog.NetworkLog.requestForConsoleMessage(l);e&&n.NetworkManager.NetworkManager.canReplayRequest(e)&&s.debugSection().appendItem(Be(Pe.replayXhr),n.NetworkManager.NetworkManager.replayRequest.bind(null,e),{jslogContext:"replay-xhr"})}s.show()}async saveConsole(){const t=n.TargetManager.TargetManager.instance().scopeTarget().inspectedURL(),i=e.ParsedURL.ParsedURL.fromString(t),r=s.StringUtilities.sprintf("%s-%d.log",i?i.host:"console",Date.now()),a=new m.FileUtils.FileOutputStream,l=new o.ProgressIndicator.ProgressIndicator;l.setTitle(Be(Pe.writingFile)),l.setTotalWork(this.itemCount());if(!await a.open(r))return;this.progressToolbarItem.element.appendChild(l.element);let c=0;for(;c<this.itemCount()&&!l.isCanceled();){const e=[];let t;for(t=0;t<350&&t+c<this.itemCount();++t){const s=this.itemElement(c+t);e.push(s.toExportString())}c+=t,await a.write(e.join("\n")+"\n"),l.setWorked(c)}a.close(),l.done()}tryToCollapseMessages(e,t){return!(this.timestampsSetting.get()||!t||e.consoleMessage().isGroupMessage()||e.consoleMessage().type===n.ConsoleModel.FrontendMessageType.Command||e.consoleMessage().type===n.ConsoleModel.FrontendMessageType.Result||!e.consoleMessage().isEqual(t.consoleMessage()))&&(t.incrementRepeatCount(),e.isLastInSimilarGroup()&&t.setInSimilarGroup(!0,!0),!0)}buildHiddenCache(e,t){const s=Date.now();let n;for(n=e;n<t.length&&(this.computeShouldMessageBeVisible(t[n]),!(n%10==0&&Date.now()-s>12));++n);n!==t.length?this.buildHiddenCacheTimeout=this.element.window().requestAnimationFrame(this.buildHiddenCache.bind(this,n+1,t)):this.updateMessageList()}cancelBuildHiddenCache(){this.shouldBeHiddenCache.clear(),this.buildHiddenCacheTimeout&&(this.element.window().cancelAnimationFrame(this.buildHiddenCacheTimeout),delete this.buildHiddenCacheTimeout)}updateMessageList(){this.regexMatchRanges=[],this.hiddenByFilterCount=0;for(const e of this.visibleViewMessages)e.resetCloseGroupDecorationCount(),e.resetIncrementRepeatCount();if(this.visibleViewMessages=[],this.groupSimilarSetting.get())this.addGroupableMessagesToEnd();else for(const e of this.consoleMessages)e.setInSimilarGroup(!1),e.consoleMessage().isGroupable()&&e.clearConsoleGroup(),this.appendMessageToEnd(e,!0);this.updateFilterStatus(),this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length),this.viewport.invalidate(),this.messagesCountElement.setAttribute("aria-label",Be(Pe.filteredMessagesInConsole,{PH1:this.visibleViewMessages.length}))}addGroupableMessagesToEnd(){const e=new Set,t=new Set;for(const s of this.consoleMessages){const o=s.consoleMessage();if(e.has(o))continue;if(!o.isGroupable()){this.appendMessageToEnd(s),e.add(o);continue}const i=s.groupKey(),r=this.groupableMessages.get(i);if(!r||r.length<5){s.setInSimilarGroup(!1),this.appendMessageToEnd(s),e.add(o);continue}if(t.has(i))continue;if(!r.find((e=>this.shouldMessageBeVisible(e)))){for(const t of r)e.add(t.consoleMessage());t.add(i);continue}let a=this.groupableMessageTitle.get(i);if(!a){const e=new n.ConsoleModel.ConsoleMessage(null,o.source,o.level,s.groupTitle(),{type:"startGroupCollapsed"});a=this.createViewMessage(e),this.groupableMessageTitle.set(i,a)}a.setRepeatCount(r.length),this.appendMessageToEnd(a);for(const t of r)t.setInSimilarGroup(!0,r[r.length-1]===t),t.setConsoleGroup(a),this.appendMessageToEnd(t,!0),e.add(t.consoleMessage());const l=new n.ConsoleModel.ConsoleMessage(null,o.source,o.level,o.messageText,{type:"endGroup"});this.appendMessageToEnd(this.createViewMessage(l))}}messagesClicked(e){const t=e.target;if(!this.messagesElement.hasSelection()){(t===this.messagesElement||this.prompt.belowEditorElement().isSelfOrAncestor(t))&&(this.prompt.moveCaretToEndOfPrompt(),this.focusPrompt())}}messagesKeyDown(e){const t=e;t.ctrlKey||t.altKey||t.metaKey||1!==t.key.length||o.UIUtils.isEditing()||this.messagesElement.hasSelection()||(this.prompt.moveCaretToEndOfPrompt(),this.focusPrompt())}messagesPasted(e){r.Runtime.Runtime.queryParam("isChromeForTesting")||r.Runtime.Runtime.queryParam("disableSelfXssWarnings")||this.selfXssWarningDisabledSetting.get()||(e.preventDefault(),this.prompt.showSelfXssWarning()),o.UIUtils.isEditing()||this.prompt.focus()}registerShortcuts(){this.shortcuts.set(o.KeyboardShortcut.KeyboardShortcut.makeKey("u",o.KeyboardShortcut.Modifiers.Ctrl),this.clearPromptBackwards.bind(this))}clearPromptBackwards(e){this.prompt.clear(),h.logKeyDown(e.currentTarget,e,"clear-prompt")}promptKeyDown(e){const t=e;if("PageUp"===t.key)return void this.updateStickToBottomOnWheel();const s=o.KeyboardShortcut.KeyboardShortcut.makeKeyFromEvent(t),n=this.shortcuts.get(s);n&&(n(t),t.preventDefault())}printResult(e,t,s){if(!e)return;const o=Boolean(s)?"error":"info";let i;i=s?n.ConsoleModel.ConsoleMessage.fromException(e.runtimeModel(),s,n.ConsoleModel.FrontendMessageType.Result,void 0,void 0):new n.ConsoleModel.ConsoleMessage(e.runtimeModel(),"javascript",o,"",{type:n.ConsoleModel.FrontendMessageType.Result,parameters:[e]}),i.setOriginatingMessage(t),e.runtimeModel().target().model(n.ConsoleModel.ConsoleModel)?.addMessage(i)}commandEvaluated(e){const{data:t}=e;this.printResult(t.result,t.commandMessage,t.exceptionDetails)}elementsToRestoreScrollPositionsFor(){return[this.messagesElement]}onSearchCanceled(){this.cleanupAfterSearch();for(const e of this.visibleViewMessages)e.setSearchRegex(null);this.currentMatchRangeIndex=-1,this.regexMatchRanges=[],this.searchRegex=null,this.viewport.refresh()}performSearch(e,t,s){this.onSearchCanceled(),this.searchableViewInternal.updateSearchMatchesCount(0),this.searchRegex=e.toSearchRegex(!0).regex,this.regexMatchRanges=[],this.currentMatchRangeIndex=-1,t&&(this.searchShouldJumpBackwards=Boolean(s)),this.searchProgressIndicator=new o.ProgressIndicator.ProgressIndicator,this.searchProgressIndicator.setTitle(Be(Pe.searching)),this.searchProgressIndicator.setTotalWork(this.visibleViewMessages.length),this.progressToolbarItem.element.appendChild(this.searchProgressIndicator.element),this.innerSearch(0)}cleanupAfterSearch(){delete this.searchShouldJumpBackwards,this.innerSearchTimeoutId&&(clearTimeout(this.innerSearchTimeoutId),delete this.innerSearchTimeoutId),this.searchProgressIndicator&&(this.searchProgressIndicator.done(),delete this.searchProgressIndicator)}searchFinishedForTests(){}innerSearch(e){if(delete this.innerSearchTimeoutId,this.searchProgressIndicator&&this.searchProgressIndicator.isCanceled())return void this.cleanupAfterSearch();const t=Date.now();for(;e<this.visibleViewMessages.length&&Date.now()-t<100;++e)this.searchMessage(e);if(this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length),void 0!==this.searchShouldJumpBackwards&&this.regexMatchRanges.length&&(this.jumpToMatch(this.searchShouldJumpBackwards?-1:0),delete this.searchShouldJumpBackwards),e===this.visibleViewMessages.length)return this.cleanupAfterSearch(),void window.setTimeout(this.searchFinishedForTests.bind(this),0);this.innerSearchTimeoutId=window.setTimeout(this.innerSearch.bind(this,e),100),this.searchProgressIndicator&&this.searchProgressIndicator.setWorked(e)}searchMessage(e){const t=this.visibleViewMessages[e];t.setSearchRegex(this.searchRegex);for(let s=0;s<t.searchCount();++s)this.regexMatchRanges.push({messageIndex:e,matchIndex:s})}jumpToNextSearchResult(){this.jumpToMatch(this.currentMatchRangeIndex+1)}jumpToPreviousSearchResult(){this.jumpToMatch(this.currentMatchRangeIndex-1)}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}jumpToMatch(e){if(!this.regexMatchRanges.length)return;let t;if(this.currentMatchRangeIndex>=0){t=this.regexMatchRanges[this.currentMatchRangeIndex];this.visibleViewMessages[t.messageIndex].searchHighlightNode(t.matchIndex).classList.remove(o.UIUtils.highlightedCurrentSearchResultClassName)}e=s.NumberUtilities.mod(e,this.regexMatchRanges.length),this.currentMatchRangeIndex=e,this.searchableViewInternal.updateCurrentMatchIndex(e),t=this.regexMatchRanges[e];const n=this.visibleViewMessages[t.messageIndex].searchHighlightNode(t.matchIndex);n.classList.add(o.UIUtils.highlightedCurrentSearchResultClassName),this.viewport.scrollItemIntoView(t.messageIndex),n.scrollIntoViewIfNeeded()}updateStickToBottomOnPointerDown(e){this.muteViewportUpdates=!e,this.viewport.setStickToBottom(!1),this.waitForScrollTimeout&&(clearTimeout(this.waitForScrollTimeout),delete this.waitForScrollTimeout)}updateStickToBottomOnPointerUp(){this.muteViewportUpdates&&(this.waitForScrollTimeout=window.setTimeout(function(){this.muteViewportUpdates=!1,this.isShowing()&&this.viewport.setStickToBottom(this.isScrolledToBottom());this.maybeDirtyWhileMuted&&(this.scheduleViewportRefresh(),delete this.maybeDirtyWhileMuted);delete this.waitForScrollTimeout,this.updateViewportStickinessForTest()}.bind(this),200))}updateViewportStickinessForTest(){}updateStickToBottomOnWheel(){this.updateStickToBottomOnPointerDown(),this.updateStickToBottomOnPointerUp()}promptTextChanged(){const e=this.viewport.stickToBottom(),t=this.isScrolledToBottom();this.viewport.setStickToBottom(t),t&&!e&&this.scheduleViewportRefresh(),this.promptTextChangedForTest()}promptTextChangedForTest(){}isScrolledToBottom(){return this.messagesElement.scrollHeight-this.messagesElement.scrollTop-this.messagesElement.clientHeight-this.prompt.belowEditorElement().offsetHeight<=2}}globalThis.Console=globalThis.Console||{},globalThis.Console.ConsoleView=je;class Oe{filterChanged;messageLevelFiltersSetting;hideNetworkMessagesSetting;filterByExecutionContextSetting;suggestionBuilder;textFilterUI;textFilterSetting;filterParser;currentFilter;levelLabels;levelMenuButton;constructor(t){this.filterChanged=t,this.messageLevelFiltersSetting=Oe.levelFilterSetting(),this.hideNetworkMessagesSetting=e.Settings.Settings.instance().moduleSetting("hide-network-messages"),this.filterByExecutionContextSetting=e.Settings.Settings.instance().moduleSetting("selected-context-filter-enabled"),this.messageLevelFiltersSetting.addChangeListener(this.onFilterChanged.bind(this)),this.hideNetworkMessagesSetting.addChangeListener(this.onFilterChanged.bind(this)),this.filterByExecutionContextSetting.addChangeListener(this.onFilterChanged.bind(this)),o.Context.Context.instance().addFlavorChangeListener(n.RuntimeModel.ExecutionContext,this.onFilterChanged,this);const s=Object.values(F);this.suggestionBuilder=new o.FilterSuggestionBuilder.FilterSuggestionBuilder(s),this.textFilterUI=new o.Toolbar.ToolbarFilter(void 0,1,1,Be(Pe.egEventdCdnUrlacom),this.suggestionBuilder.completions.bind(this.suggestionBuilder),!0),this.textFilterSetting=e.Settings.Settings.instance().createSetting("console.text-filter",""),this.textFilterSetting.get()&&this.textFilterUI.setValue(this.textFilterSetting.get()),this.textFilterUI.addEventListener("TextChanged",(()=>{this.textFilterSetting.set(this.textFilterUI.value()),this.onFilterChanged()})),this.filterParser=new i.TextUtils.FilterParser(s),this.currentFilter=new R("",[],null,this.messageLevelFiltersSetting.get()),this.updateCurrentFilter(),this.levelLabels=new Map([["verbose",Be(Pe.verbose)],["info",Be(Pe.info)],["warning",Be(Pe.warnings)],["error",Be(Pe.errors)]]),this.levelMenuButton=new o.Toolbar.ToolbarMenuButton(this.appendLevelMenuItems.bind(this),void 0,void 0,"log-level"),this.updateLevelMenuButtonText(),this.messageLevelFiltersSetting.addChangeListener(this.updateLevelMenuButtonText.bind(this))}onMessageAdded(e){e.type===n.ConsoleModel.FrontendMessageType.Command||e.type===n.ConsoleModel.FrontendMessageType.Result||e.isGroupMessage()||(e.context&&this.suggestionBuilder.addItem(F.Context,e.context),e.source&&this.suggestionBuilder.addItem(F.Source,e.source),e.url&&this.suggestionBuilder.addItem(F.Url,e.url))}setLevelMenuOverridden(e){this.levelMenuButton.setEnabled(!e),e?this.levelMenuButton.setTitle(Be(Pe.overriddenByFilterSidebar)):this.updateLevelMenuButtonText()}static levelFilterSetting(){return e.Settings.Settings.instance().createSetting("message-level-filters",R.defaultLevelsFilterValue())}updateCurrentFilter(){const e=this.filterParser.parse(this.textFilterUI.value());for(const{key:t}of e)switch(t){case F.Context:p.userMetrics.actionTaken(p.UserMetrics.Action.ConsoleFilterByContext);break;case F.Source:p.userMetrics.actionTaken(p.UserMetrics.Action.ConsoleFilterBySource);break;case F.Url:p.userMetrics.actionTaken(p.UserMetrics.Action.ConsoleFilterByUrl)}this.hideNetworkMessagesSetting.get()&&e.push({key:F.Source,text:"network",negative:!0,regex:void 0}),this.currentFilter.executionContext=this.filterByExecutionContextSetting.get()?o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext):null,this.currentFilter.parsedFilters=e,this.currentFilter.levelsMask=this.messageLevelFiltersSetting.get()}onFilterChanged(){this.updateCurrentFilter(),this.filterChanged()}updateLevelMenuButtonText(){let e=!0,t=!0;const s=R.allLevelsFilterValue(),n=R.defaultLevelsFilterValue();let o=null;const i=this.messageLevelFiltersSetting.get(),r={Verbose:"verbose",Info:"info",Warning:"warning",Error:"error"};for(const a of Object.values(r))e=e&&i[a]===s[a],t=t&&i[a]===n[a],i[a]&&(o=o?Be(Pe.customLevels):Be(Pe.sOnly,{PH1:String(this.levelLabels.get(a))}));o=e?Be(Pe.allLevels):t?Be(Pe.defaultLevels):o||Be(Pe.hideAll),this.levelMenuButton.element.classList.toggle("warning",!e&&!t),this.levelMenuButton.setText(o),this.levelMenuButton.setTitle(Be(Pe.logLevelS,{PH1:o}))}appendLevelMenuItems(e){const t=this.messageLevelFiltersSetting,s=t.get();e.headerSection().appendItem(Be(Pe.default),(()=>t.set(R.defaultLevelsFilterValue())),{jslogContext:"default"});for(const[t,o]of this.levelLabels.entries())e.defaultSection().appendCheckboxItem(o,n.bind(null,t),{checked:s[t],jslogContext:t});function n(e){s[e]=!s[e],t.set(s)}}addMessageURLFilter(e){if(!e)return;const t=this.textFilterUI.value()?` ${this.textFilterUI.value()}`:"";this.textFilterUI.setValue(`-url:${e}${t}`),this.textFilterSetting.set(this.textFilterUI.value()),this.onFilterChanged()}shouldBeVisible(e){return this.currentFilter.shouldBeVisible(e)}clear(){this.suggestionBuilder.clear()}reset(){this.messageLevelFiltersSetting.set(R.defaultLevelsFilterValue()),this.filterByExecutionContextSetting.set(!1),this.hideNetworkMessagesSetting.set(!1),this.textFilterUI.setValue(""),this.onFilterChanged()}}const Ve=new WeakMap,Ne=new WeakMap;var Ge=Object.freeze({__proto__:null,ConsoleView:je,ConsoleViewFilter:Oe,ActionDelegate:class{handleAction(t,s){switch(s){case"console.toggle":return je.instance().hasFocus()&&o.InspectorView.InspectorView.instance().drawerVisible()?(o.InspectorView.InspectorView.instance().closeDrawer(),!0):(p.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),e.Console.Console.instance().show(),je.instance().focusPrompt(),!0);case"console.clear":return je.clearConsole(),!0;case"console.clear.history":return je.instance().clearHistory(),!0}return!1}}});let De;class We extends o.Panel.Panel{view;constructor(){super("console"),this.view=je.instance()}static instance(e={forceNew:null}){const{forceNew:t}=e;return De&&!t||(De=new We),De}static updateContextFlavor(){const e=We.instance().view;o.Context.Context.instance().setFlavor(je,e.isShowing()?e:null)}wasShown(){super.wasShown();_e&&_e.isShowing()&&o.InspectorView.InspectorView.instance().setDrawerMinimized(!0),this.view.show(this.element),We.updateContextFlavor()}willHide(){super.willHide(),o.InspectorView.InspectorView.instance().setDrawerMinimized(!1),_e&&_e.showViewInWrapper(),We.updateContextFlavor()}searchableView(){return je.instance().searchableView()}}let _e=null;class ze extends o.Widget.VBox{view;constructor(){super(),this.view=je.instance(),this.element.setAttribute("jslog",`${h.panel("console").track({resize:!0})}`)}static instance(){return _e||(_e=new ze),_e}wasShown(){We.instance().isShowing()?o.InspectorView.InspectorView.instance().setDrawerMinimized(!0):this.showViewInWrapper(),We.updateContextFlavor()}willHide(){o.InspectorView.InspectorView.instance().setDrawerMinimized(!1),We.updateContextFlavor()}showViewInWrapper(){this.view.show(this.element)}}var qe=Object.freeze({__proto__:null,ConsolePanel:We,WrapperView:ze,ConsoleRevealer:class{async reveal(e){const t=je.instance();t.isShowing()?t.focus():await o.ViewManager.ViewManager.instance().showView("console-view")}}});const $e=new CSSStyleSheet;$e.replaceSync("#console-prompt .CodeMirror{padding:3px 0 1px}#console-prompt .CodeMirror-line{padding-top:0}#console-prompt .CodeMirror-lines{padding-top:0}#console-prompt .console-prompt-icon{position:absolute;left:-13px;top:2px;user-select:none}.console-eager-preview{padding-bottom:2px;opacity:60%;position:relative}.console-eager-inner-preview{text-overflow:ellipsis;overflow:hidden;margin-left:4px;height:100%;white-space:nowrap}.preview-result-icon{position:absolute;left:-13px;top:-1px}.console-eager-inner-preview:empty,\n.console-eager-inner-preview:empty + .preview-result-icon{opacity:0%}.console-prompt-icon.console-prompt-incomplete{opacity:65%}\n/*# sourceURL=consolePrompt.css */\n");const{Direction:Ke}=l.TextEditorHistory,Je={consolePrompt:"Console prompt",selfXssWarning:"Warning: Don’t paste code into the DevTools Console that you don’t understand or haven’t reviewed yourself. This could allow attackers to steal your identity or take control of your computer. Please type ‘{PH1}’ below and hit Enter to allow pasting.",allowPasting:"allow pasting"},Xe=t.i18n.registerUIStrings("panels/console/ConsolePrompt.ts",Je),Ze=t.i18n.getLocalizedString.bind(void 0,Xe);class Qe extends(e.ObjectWrapper.eventMixin(o.Widget.Widget)){addCompletionsFromHistory;historyInternal;initialText;editor;eagerPreviewElement;textChangeThrottler;formatter;requestPreviewBound;requestPreviewCurrent=0;innerPreviewElement;promptIcon;iconThrottler;previewRequestForTest;highlightingNode;#a;#l;#c=!1;#h=new a.Compartment;#d(){return this.#c?[]:"true"!==r.Runtime.Runtime.queryParam("noJavaScriptCompletion")?[a.javascript.javascript(),l.JavaScript.completion()]:[a.javascript.javascriptLanguage]}#u(){const e=this.#d(),t=this.#h.reconfigure(e);this.editor.dispatch({effects:t})}constructor(){super(),this.addCompletionsFromHistory=!0,this.historyInternal=new l.AutocompleteHistory.AutocompleteHistory(e.Settings.Settings.instance().createLocalSetting("console-history",[])),this.initialText="",this.eagerPreviewElement=document.createElement("div"),this.eagerPreviewElement.classList.add("console-eager-preview"),this.textChangeThrottler=new e.Throttler.Throttler(150),this.formatter=new c.RemoteObjectPreviewFormatter.RemoteObjectPreviewFormatter,this.requestPreviewBound=this.requestPreview.bind(this),this.innerPreviewElement=this.eagerPreviewElement.createChild("div","console-eager-inner-preview");const t=new d.Icon.Icon;t.data={iconName:"chevron-left-dot",color:"var(--icon-default)",width:"16px",height:"16px"},t.classList.add("preview-result-icon"),this.eagerPreviewElement.appendChild(t);const s=this.element.createChild("div","console-prompt-editor-container");this.element.appendChild(this.eagerPreviewElement),this.promptIcon=new d.Icon.Icon,this.promptIcon.data={iconName:"chevron-right",color:"var(--icon-action)",width:"16px",height:"16px"},this.promptIcon.classList.add("console-prompt-icon"),this.element.appendChild(this.promptIcon),this.iconThrottler=new e.Throttler.Throttler(0),this.eagerPreviewElement.classList.toggle("hidden",!0),this.element.tabIndex=0,this.previewRequestForTest=null,this.highlightingNode=!1;const n=l.JavaScript.argumentHints();this.#a=n[0];const o=l.Config.DynamicSetting.bool("console-autocomplete-on-enter",[],l.Config.conservativeCompletion),i=[a.keymap.of(this.editorKeymap()),a.EditorView.updateListener.of((e=>this.editorUpdate(e))),n,o.instance(),l.Config.showCompletionHint,l.Config.baseConfiguration(this.initialText),l.Config.autocompletion.instance(),a.javascript.javascriptLanguage.data.of({autocomplete:e=>this.addCompletionsFromHistory?this.#l.historyCompletions(e):null}),a.EditorView.contentAttributes.of({"aria-label":Ze(Je.consolePrompt)}),a.EditorView.lineWrapping,a.autocompletion({aboveCursor:!0}),this.#h.of(this.#d())],r=this.initialText,u=a.EditorState.create({doc:r,extensions:i});this.editor=new l.TextEditor.TextEditor(u),this.editor.addEventListener("keydown",(e=>{e.defaultPrevented&&e.stopPropagation()})),s.appendChild(this.editor),this.#l=new l.TextEditorHistory.TextEditorHistory(this.editor,this.historyInternal),this.hasFocus()&&this.focus(),this.element.removeAttribute("tabindex"),this.editorSetForTest(),p.userMetrics.panelLoaded("console","DevTools.Launch.Console"),this.element.setAttribute("jslog",`${h.textField("console-prompt").track({change:!0,keydown:"Enter|ArrowUp|ArrowDown|PageUp"})}`)}belowEditorElement(){return this.eagerPreviewElement}onTextChanged(){this.updatePromptIcon(),this.dispatchEventToListeners("TextChanged")}async requestPreview(){const e=++this.requestPreviewCurrent,t=l.Config.contentIncludingHint(this.editor.editor).trim(),s=o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext),{preview:i,result:r}=await c.JavaScriptREPL.JavaScriptREPL.evaluateAndBuildPreview(t,!0,!0,500);this.requestPreviewCurrent===e&&(this.innerPreviewElement.removeChildren(),i.deepTextContent()!==l.Config.contentIncludingHint(this.editor.editor).trim()&&this.innerPreviewElement.appendChild(i),r&&"object"in r&&r.object&&"node"===r.object.subtype?(this.highlightingNode=!0,n.OverlayModel.OverlayModel.highlightObjectAsDOMNode(r.object)):this.highlightingNode&&(this.highlightingNode=!1,n.OverlayModel.OverlayModel.hideDOMNodeHighlight()),r&&s&&s.runtimeModel.releaseEvaluationResult(r))}wasShown(){super.wasShown(),this.registerCSSFiles([$e])}willHide(){this.highlightingNode&&(this.highlightingNode=!1,n.OverlayModel.OverlayModel.hideDOMNodeHighlight())}history(){return this.historyInternal}clearAutocomplete(){a.closeCompletion(this.editor.editor)}isCaretAtEndOfPrompt(){return this.editor.state.selection.main.head===this.editor.state.doc.length}moveCaretToEndOfPrompt(){this.editor.dispatch({selection:a.EditorSelection.cursor(this.editor.state.doc.length)})}clear(){this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length}})}text(){return this.editor.state.doc.toString()}setAddCompletionsFromHistory(e){this.addCompletionsFromHistory=e}editorKeymap(){return[{key:"ArrowUp",run:()=>this.#l.moveHistory(-1)},{key:"ArrowDown",run:()=>this.#l.moveHistory(1)},{mac:"Ctrl-p",run:()=>this.#l.moveHistory(-1,!0)},{mac:"Ctrl-n",run:()=>this.#l.moveHistory(1,!0)},{key:"Escape",run:()=>l.JavaScript.closeArgumentsHintsTooltip(this.editor.editor,this.#a)},{key:"Ctrl-Enter",run:()=>(this.handleEnter(!0),!0)},{key:"Enter",run:()=>(this.handleEnter(),!0),shift:a.insertNewlineAndIndent}]}async enterWillEvaluate(e){const{doc:t,selection:s}=this.editor.state;if(!t.length)return!1;if(e||s.main.head<t.length)return!0;const i=o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext),r=await l.JavaScript.isExpressionComplete(t.toString());return i===o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext)&&r}showSelfXssWarning(){e.Console.Console.instance().warn(Ze(Je.selfXssWarning,{PH1:Ze(Je.allowPasting)}),e.Console.FrontendMessageSource.SelfXss),this.#c=!0,p.userMetrics.actionTaken(p.UserMetrics.Action.SelfXssWarningConsoleMessageShown),this.#u()}async handleEnter(t){if(this.#c&&this.text()===Ze(Je.allowPasting))return e.Console.Console.instance().log(this.text()),this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length},scrollIntoView:!0}),e.Settings.Settings.instance().createSetting("disable-self-xss-warning",!1,"Synced").set(!0),this.#c=!1,p.userMetrics.actionTaken(p.UserMetrics.Action.SelfXssAllowPastingInConsole),void this.#u();await this.enterWillEvaluate(t)?(this.appendCommand(this.text(),!0),l.JavaScript.closeArgumentsHintsTooltip(this.editor.editor,this.#a),this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length},scrollIntoView:!0})):this.editor.state.doc.length?a.insertNewlineAndIndent(this.editor.editor):this.editor.dispatch({scrollIntoView:!0})}updatePromptIcon(){this.iconThrottler.schedule((async()=>{this.promptIcon.classList.toggle("console-prompt-incomplete",!await this.enterWillEvaluate())}))}appendCommand(e,t){const s=o.Context.Context.instance().flavor(n.RuntimeModel.ExecutionContext);if(s){const o=s,i=o.target().model(n.ConsoleModel.ConsoleModel);if(i){const s=i.addCommandMessage(o,e),n=c.JavaScriptREPL.JavaScriptREPL.wrapObjectLiteral(e);this.evaluateCommandInConsole(o,s,n,t),We.instance().isShowing()&&p.userMetrics.actionTaken(p.UserMetrics.Action.CommandEvaluatedInConsolePanel)}}}async evaluateCommandInConsole(e,t,s,o){const i=e.debuggerModel.selectedCallFrame();if(i&&i.script.isJavaScript()){const e=await S.NamesResolver.allVariablesInCallFrame(i);s=await this.substituteNames(s,e)}await(e.target().model(n.ConsoleModel.ConsoleModel)?.evaluateCommandInConsole(e,t,s,o))}async substituteNames(e,t){try{return await w.FormatterWorkerPool.formatterWorkerPool().javaScriptSubstitute(e,t)}catch{return e}}editorUpdate(e){e.docChanged||a.selectedCompletion(e.state)!==a.selectedCompletion(e.startState)?this.onTextChanged():e.selectionSet&&this.updatePromptIcon()}focus(){this.editor.focus()}editorSetForTest(){}}var Ye=Object.freeze({__proto__:null,ConsolePrompt:Qe});export{L as ConsoleContextSelector,A as ConsoleFilter,j as ConsoleFormat,qe as ConsolePanel,q as ConsolePinPane,Ye as ConsolePrompt,se as ConsoleSidebar,Ge as ConsoleView,Le as ConsoleViewMessage,Ae as ConsoleViewport,le as ErrorStackParser};
