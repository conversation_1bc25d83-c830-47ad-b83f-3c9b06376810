{"name": "@react-native/community-cli-plugin", "version": "0.79.5", "description": "Core CLI commands for React Native", "keywords": ["react-native", "tools"], "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/community-cli-plugin#readme", "bugs": "https://github.com/facebook/react-native/issues", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/community-cli-plugin"}, "license": "MIT", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@react-native/dev-middleware": "0.79.5", "chalk": "^4.0.0", "debug": "^2.2.0", "invariant": "^2.2.4", "metro": "^0.82.0", "metro-config": "^0.82.0", "metro-core": "^0.82.0", "semver": "^7.1.3"}, "devDependencies": {"metro-resolver": "^0.82.0"}, "peerDependencies": {"@react-native-community/cli": "*"}, "peerDependenciesMeta": {"@react-native-community/cli": {"optional": true}}, "engines": {"node": ">=18"}}