/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

declare function filterPlatformAssetScales(
  platform: string,
  scales: $ReadOnlyArray<number>
): $ReadOnlyArray<number>;

declare export default typeof filterPlatformAssetScales;
