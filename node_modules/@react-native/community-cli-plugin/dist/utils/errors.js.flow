/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

/**
 * A custom Error that creates a single-lined message to match current styling inside CLI.
 * Uses original stack trace when `originalError` is passed or erase the stack if it's not defined.
 */
declare export class CLIError extends Error {
  constructor(msg: string, originalError?: Error | string): void;
}

/**
 * Raised when we're unable to find a package.json
 */
declare export class UnknownProjectError extends Error {}

declare export const inlineString: (str: string) => string;
