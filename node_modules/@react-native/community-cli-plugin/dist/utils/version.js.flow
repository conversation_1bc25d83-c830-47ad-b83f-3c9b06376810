/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { Config } from "@react-native-community/cli-types";
import type TerminalReporter from "metro/src/lib/TerminalReporter";

type Release = {
  // The current stable release
  stable: string,
  // The current candidate release. These are only populated if the latest release is a candidate release.
  candidate?: string,
  changelogUrl: string,
  diffUrl: string,
};

/**
 * Logs out a message if the user's version is behind a stable version of React Native
 */
declare export function logIfUpdateAvailable(
  cliConfig: Config,
  reporter: TerminalReporter
): Promise<void>;

/**
 * Checks via GitHub API if there is a newer stable React Native release and,
 * if it exists, returns the release data.
 *
 * If the latest release is not newer or if it's a prerelease, the function
 * will return undefined.
 */
declare export default function getLatestRelease(
  currentVersion: string
): Promise<Release | void>;
