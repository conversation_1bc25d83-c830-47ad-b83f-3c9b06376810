# AI Agent 快速参考卡片

## 🚨 关键约束（必须遵守）

### Shell环境
```bash
# ❌ 错误：直接使用sudo（丢失.bashrc配置）
sudo make deploy

# ✅ 正确：先进入root环境
sudo -i
cd 到对应项目目录
make deploy
```

### 服务部署流程
```bash
# 标准三步骤
cd personal_service_biz/[service-name]
make build          # 普通用户权限
sudo -i             # 切换到root
cd 到对应项目目录
make deploy         # root权限部署
```

### 网络访问
```bash
# ❌ 禁止使用
127.0.0.1:16135
localhost:8080

# ✅ 统一使用
https://local.luzeshu.cn/api
```

## 🏗️ 架构速览

### 服务端口表
| 服务 | 端口 | 容器名 |
|------|------|--------|
| Gateway | 16135 | gateway |
| Favorites | 18081 | favorites-service |
| Task | 18082 | task-service |
| Notes | 18083 | notes-service |
| Drive | 18084 | drive-service |
| Nginx | 443/80 | nginx-admin |

### 请求链路
```
外网 → SSH隧道 → nginx-admin:443 → gateway:16135 → 微服务
```

## 🔧 常用命令

### 服务状态检查
```bash
# 检查容器
docker ps | grep -E "(gateway|favorites|task|notes|drive)"

# 检查日志（使用文件路径，不用docker logs）
tail -f /data/docker-volumes/[service]/runtime/[service]-logs/stderr.log
tail -f /data/docker-volumes/gateway/runtime/gateway-logs/stderr.log

# 测试API
curl -H "bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  https://local.luzeshu.cn/api/favorites/list

# 测试文件下载API
curl -H "bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  https://local.luzeshu.cn/api/drive/files/[file_id]/download -v
```

### 调试命令
```bash
# ADB调试 - 跨网络连接
# Mac和手机在同一局域网，通过SSH隧道连接到本地
ssh -R "19900:手机ip:手机调试端口" user@local-server
adb connect 127.0.0.1:19900  # 连接手机无线调试

# ADB端口转发（应用内网络）
adb reverse tcp:8080 tcp:443

# 重启服务
sudo -i
cd /data/projects/personal/personal_service_biz/[service]
make deploy

# 清理容器
docker container rm -f [container-name]
```

## 📱 客户端开发

### React Native配置
- **API地址**: `https://local.luzeshu.cn/api`
- **认证头**: `bullet: 36fdf066-9e42-11ec-b41e-525400043ced`
- **端口转发**: `adb reverse tcp:8080 tcp:443`

### 测试流程
```bash
cd personal_clients/thinking-react-native
npx tsc --noEmit --skipLibCheck  # 编译检查
npm run android                  # 构建测试
node integration_test.js         # API测试
```

## 🗄️ 数据库与存储

### 数据库操作
```bash
# 创建数据库
cd /data/projects/personal/admin/dbmanager/scripts
./create_db.sh [db_name]
```

### 存储路径
- **日志**: `/data/docker-volumes/[service]/runtime/[service]-logs/`
- **文件**: `/data/docker-volumes/public_root/file_resources`

## 📦 包管理规范

```bash
# ⚠️ 网络问题时需要先配置代理（依赖.bashrc）
sudo -i  # 确保加载.bashrc配置
oproxy_ks  # 配置网络代理环境

# ✅ 使用包管理器
npm install [package]
go mod tidy
pip install [package]

# ❌ 避免手动编辑
# 不要直接编辑 package.json, go.mod, requirements.txt
```

## 🔍 故障排除

### 网络问题
1. 检查SSH隧道状态
2. 验证nginx-admin容器运行
3. 测试域名解析：`nslookup local.luzeshu.cn`
4. 包下载失败时配置代理：`sudo -i && oproxy_ks`

### ADB调试问题
1. 检查SSH隧道：`ssh -R "19900:手机ip:手机调试端口" user@server`
2. 连接手机：`adb connect 127.0.0.1:19900`
3. 验证连接：`adb devices`
4. 端口转发：`adb reverse tcp:8080 tcp:443`

### 服务问题
1. 检查容器状态：`docker ps -a`
2. 查看服务日志（使用文件路径）
3. 验证端口配置

### 图片预览问题
1. 检查API调用：`curl -H "bullet: 36fdf066-9e42-11ec-b41e-525400043ced" https://local.luzeshu.cn/api/drive/files/[id]/download -v`
2. 确认FastImage使用带认证头的URL：`driveService.getDownloadUrlWithHeaders(fileId)`
3. 检查nginx日志：`tail -f /data/docker-volumes/gateway/runtime/gateway-logs/stderr.log`
4. 验证React Native网络权限配置

### 权限问题
1. 构建阶段使用普通用户
2. 部署阶段使用root（sudo -i）
3. 检查文件所有者权限

## ⚠️ 重要提醒

1. **任何sudo命令前必须先`sudo -i`**（加载.bashrc配置）
2. **网络访问统一使用`https://local.luzeshu.cn`**
3. **端口配置必须固定，禁止动态分配**
4. **日志查看使用文件路径，不用`docker logs`**
5. **ADB调试使用SSH隧道：`ssh -R "19900:手机ip:调试端口"`**
6. **包下载失败时先配置代理：`sudo -i && oproxy_ks`**
7. **测试完整性：确保端到端功能正常**
8. **包管理：使用对应语言的包管理器**

---
📖 详细文档请参考：[README-for-AI.md](./README-for-AI.md)
