# 开发环境规范（AI Agent上下文）

## 一、关键约束与常见错误

### 1.1 Shell环境
- **禁止直接使用 `sudo command`**（会丢失`.bashrc`配置）
- **正确做法**：需要root权限时使用：
  ```bash
  sudo -i  # 先进入root环境加载配置
  command   # 再执行命令
  ```

### 1.2 服务调试流程
- **标准流程**：进入服务目录 → `make build` → `sudo -i` → `make deploy`
- **日志查看**：`/data/docker-volumes/<服务名>/runtime/<服务名>-logs/`（非`docker logs`）
- **容器网络**：所有服务运行在`admin-net`网络中，通过容器名通信

### 1.3 网络访问规则
- **禁止使用`127.0.0.1`或`localhost`**访问服务
- **统一访问入口**：`https://local.luzeshu.cn`
- **端口必须固定**（禁止自动切换端口，如React Native Metro默认8081）
- **SSH隧道架构**：外网 → SSH隧道 → nginx-admin容器 → gateway容器 → 微服务容器

### 1.4 跨网络调试
- **ADB调试架构**：Mac和手机在同一局域网，通过SSH隧道连接到本地服务器
- **SSH隧道建立**：`ssh -R "19900:手机ip:手机调试端口" user@local-server`
- **ADB连接**：`adb connect 127.0.0.1:19900`（连接手机无线调试）
- **应用端口转发**：`adb reverse tcp:8080 tcp:443`（将手机8080映射到本地443）
- **固定端口范围**：避免动态端口分配导致隧道失效

## 二、微服务架构

### 2.1 服务组件
```
外网请求 → SSH隧道 → nginx-admin:443 → gateway:16135 → 微服务
                                                    ├── favorites-service:18081
                                                    ├── task-service:18082
                                                    ├── notes-service:18083
                                                    └── drive-service:18084
```

### 2.2 核心服务部署
| 服务名 | 端口 | 部署路径 | 容器名 |
|--------|------|----------|--------|
| Gateway | 16135 | personal_service_biz/gateway | gateway |
| Favorites | 18081 | personal_service_biz/favorites-service | favorites-service |
| Task | 18082 | personal_service_biz/task-service | task-service |
| Notes | 18083 | personal_service_biz/notes-service | notes-service |
| Drive | 18084 | personal_service_biz/drive-service | drive-service |
| Nginx | 443/80 | admin/nginx-admin | nginx-admin |

### 2.3 标准部署流程
```bash
# 1. 构建服务（普通用户权限）
cd personal_service_biz/[service-name]
make build

# 2. 部署服务（需要root权限）
sudo -i
make deploy

# 3. 验证部署
docker ps | grep [service-name]
```

## 三、数据库与存储

### 3.1 数据库操作
```bash
# 创建数据库
cd /data/projects/personal/admin/dbmanager/scripts
./create_db.sh [db_name]

# 更新数据库结构
cd /data/projects/personal/admin/admin
./onekey.sh  # 包含数据库初始化
```

### 3.2 存储路径
- **开发环境**：`/data/docker-volumes/public_root/file_resources`
- **生产环境**：`/store_root`
- **日志目录**：`/data/docker-volumes/[service-name]/runtime/[service-name]-logs/`

## 四、客户端开发

### 4.1 React Native配置
- **API地址**：`https://local.luzeshu.cn/api`
- **认证头**：`bullet: 36fdf066-9e42-11ec-b41e-525400043ced`
- **网络配置**：允许HTTPS和明文流量（开发环境）
- **端口转发**：`adb reverse tcp:8080 tcp:443`

### 4.2 测试流程
```bash
# 1. 编译检查
cd personal_clients/thinking-react-native
npx tsc --noEmit --skipLibCheck

# 2. 构建测试
npm run android

# 3. API集成测试
node integration_test.js
```

## 五、常见问题与解决方案

### 5.1 网络连接问题
- **症状**：API请求失败、超时
- **检查**：SSH隧道是否正常、nginx-admin容器状态
- **解决**：重启SSH隧道、检查域名解析

### 5.2 服务启动失败
- **症状**：容器无法启动、端口冲突
- **检查**：`docker ps -a`、端口占用情况
- **解决**：清理旧容器、检查端口配置

### 5.3 权限问题
- **症状**：git操作失败、文件权限错误
- **检查**：当前用户权限、文件所有者
- **解决**：使用正确的用户执行操作

## 六、开发规范

### 6.1 新增服务模板
参考`favorites-service`结构：
```
service-name/
├── Makefile          # 标准构建部署脚本
├── scripts/
│   ├── deploy.sh     # 部署脚本
│   └── test.sh       # 测试脚本
├── image/
│   └── Dockerfile    # 容器镜像
├── config.ini        # 服务配置
└── main.go          # 服务入口
```

### 6.2 Thrift接口更新
```bash
# 1. 修改IDL文件
cd personal_common/thrift-common

# 2. 生成代码（需要root权限）
sudo -i
make gen

# 3. 更新服务代码
# 在各服务中引用新生成的代码
```

### 6.3 包管理规范
- **网络问题处理**：包下载失败时先配置代理
  ```bash
  sudo -i  # 确保加载.bashrc配置
  oproxy_ks  # 配置网络代理环境
  ```
- **JavaScript/Node.js**：使用`npm install`，避免手动编辑package.json
- **Go**：使用`go mod tidy`，避免手动编辑go.mod
- **Python**：使用`pip install`，避免手动编辑requirements.txt

### 6.4 测试规范
- **单元测试**：每个服务都有对应的测试脚本`make test`
- **集成测试**：使用Node.js脚本测试完整API链路
- **部署验证**：部署后必须验证服务链路正常
- **客户端测试**：React Native需要运行`npm run android`验证

## 七、消息队列与异步处理

### 7.1 RabbitMQ队列
- **bilibili下载队列**：`favorites_pull_resource_bilibili_start`
- **微信文章队列**：`favorites_pull_resource_wx_start`
- **状态回传队列**：`favorites_pull_resource_status`

### 7.2 异步处理流程
```
Gateway → Favorites Service → RabbitMQ → Bilibili Crawler
                           ↓
                    Database Update ← Status Callback
```

## 八、调试与故障排除

### 8.1 服务状态检查
```bash
# 检查容器状态
docker ps | grep -E "(gateway|favorites|task|notes|drive)"

# 检查网络连通性
curl -H "bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  https://local.luzeshu.cn/api/favorites/list

# 检查日志
tail -f /data/docker-volumes/[service-name]/runtime/[service-name]-logs/[service-name].log
```

### 8.2 常用调试命令
```bash
# 进入容器调试
docker exec -it [container-name] /bin/bash

# 重启服务
sudo -i
cd /data/projects/personal/personal_service_biz/[service-name]
make deploy

# 清理旧容器
docker container rm -f [container-name]
```

### 8.3 网络调试
```bash
# ADB调试 - SSH隧道建立
ssh -R "19900:手机ip:手机调试端口" user@local-server

# ADB连接手机
adb connect 127.0.0.1:19900
adb devices  # 验证连接

# 应用端口转发
adb reverse tcp:8080 tcp:443
adb reverse --list

# 测试域名解析
nslookup local.luzeshu.cn

# 包下载网络问题
sudo -i && oproxy_ks  # 配置代理环境
```

## 九、环境变量与配置

### 9.1 关键环境变量
- `ENV=deploy`：生产环境标识
- `TREETHEME_INSTALL_MODE`：安装模式配置
- `LANG=C.UTF-8`：字符编码设置

### 9.2 配置文件位置
- **服务配置**：`[service]/config.ini`
- **Nginx配置**：`admin/nginx-admin/conf/sites-available/`
- **Docker网络**：`admin-net`

## 十、安全与权限

### 10.1 API认证
- **统一认证头**：`bullet: 36fdf066-9e42-11ec-b41e-525400043ced`
- **HTTPS强制**：所有API调用必须使用HTTPS
- **跨域配置**：允许本地开发环境访问

### 10.2 文件权限
- **构建阶段**：使用普通用户权限（避免git权限问题）
- **部署阶段**：使用root权限（Docker操作需要）
- **日志文件**：确保服务有写入权限

> **重要提醒**：
> 1. 任何`sudo`命令执行前必须先`sudo -i`（加载.bashrc配置）
> 2. 服务部署必须按标准流程：build → sudo -i → deploy
> 3. 网络访问统一使用`https://local.luzeshu.cn`
> 4. 端口配置必须固定，禁止动态分配
> 5. 日志查看使用文件路径，不使用`docker logs`
> 6. ADB调试使用SSH隧道：`ssh -R "19900:手机ip:调试端口" user@server`
> 7. 包下载失败时先配置代理：`sudo -i && oproxy_ks`
> 8. 测试完整性：确保端到端功能正常后才完成任务
> 9. 包管理：使用对应语言的包管理器，避免手动编辑配置文件
